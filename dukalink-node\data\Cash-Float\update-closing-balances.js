const path = require("path");
const {
  Branch,
  PosSession,
  PosSessionReconciliation,
} = require("../../src/models");
const sequelize = require("../../config/database");
const logger = require("../../src/utils/logger");

/**
 * <PERSON><PERSON><PERSON> to update closing balances for POS sessions based on Excel data
 * Uses branch name to find POS session, then updates reconciliation table
 *
 * Database Analysis:
 * - 164 total POS sessions across 44 branches
 * - 153 existing reconciliation records
 * - Most branches have 3-6 sessions with recent activity on 2025-05-27
 * - Some branches like "Mombasa " have sessions but no reconciliation records
 * - Branch names corrected to match database exactly (e.g., "Mau summit", "Mombasa ")
 */

// Excel data from 27th balances.xlsx (manually extracted for simplicity)
const balancesData = [
  { branch: "Bungoma", cash: 13449, float: 10257 },
  { branch: "Busia", cash: 17363, float: 14519 },
  { branch: "<PERSON><PERSON>", cash: 21801, float: 1928 },
  { branch: "<PERSON><PERSON>", cash: 16150, float: 55958 },
  { branch: "Homabay", cash: 70312, float: 4445 },
  { branch: "Kapenguria", cash: 13325, float: 4161 },
  { branch: "<PERSON><PERSON><PERSON><PERSON>", cash: 5270, float: 13395 },
  { branch: "Karatina", cash: 5425, float: 2073 },
  { branch: "Kathonzweni", cash: 29304, float: 935 },
  { branch: "Kehancha", cash: 750, float: 20832 },
  { branch: "Kericho", cash: 20040, float: 3709 },
  { branch: "Kerugoya", cash: 23390, float: 3792 },
  { branch: "Kibwezi", cash: 16430, float: 17145 },
  { branch: "Kikima", cash: 7985, float: 80133 },
  { branch: "Kilgoris", cash: 30923, float: 11159 },
  { branch: "Kilifi", cash: 7275, float: 26675 },
  { branch: "Kisumu", cash: 72153, float: 43634 },
  { branch: "Kitale 2", cash: 24014, float: 14414 },
  { branch: "Kitui", cash: 21014, float: 2016 },
  { branch: "Likoni", cash: 34535, float: 17243 },
  { branch: "Machakos", cash: 28009, float: 13725 },
  { branch: "Magunga", cash: 9805, float: 26438 },
  { branch: "Malaba", cash: 21463, float: 24168 },
  { branch: "Mariakani 2", cash: 4748, float: 19750 },
  { branch: "Mau summit", cash: 34440, float: 9560 },
  { branch: "Maua", cash: 14920, float: 16424 },
  { branch: "Mbale", cash: 19145, float: 68177 },
  { branch: "Mbita", cash: 28038, float: 624 },
  { branch: "Mombasa ", cash: 34821, float: 96381 },
  { branch: "Mtwapa", cash: 5696, float: 54296 },
  { branch: "Muhuru Bay", cash: 18140, float: 15260 },
  { branch: "Mumias", cash: 3572, float: 11060 },
  { branch: "Mutomo", cash: 10365, float: 29463 },
  { branch: "Mwatate", cash: 12668, float: 9057 },
  { branch: "Mwingi", cash: 4304, float: 15261 },
  { branch: "Nkubu", cash: 18900, float: 656 },
  { branch: "Nyamira", cash: 40006, float: 1896 },
  { branch: "Nyeri", cash: 28769, float: 20628 },
  { branch: "Oyugis", cash: 12518, float: 13794 },
  { branch: "Prudential", cash: 54726, float: 10102 },
  { branch: "Sori", cash: 32, float: 2599 },
  { branch: "Webuye", cash: 39962, float: 17681 },
  { branch: "Wote", cash: 16041, float: 5890 },
  { branch: "Wundanyi", cash: 9830, float: 12458 },
];

async function updateClosingBalances() {
  const transaction = await sequelize.transaction();

  try {
    logger.info("🚀 Starting closing balances update process...");

    const updates = [];
    const errors = [];

    // Process each balance entry
    for (const balanceEntry of balancesData) {
      const {
        branch: branchName,
        cash: cashBalance,
        float: floatBalance,
      } = balanceEntry;

      if (!branchName) {
        logger.warn(`⚠️ Skipping entry: Empty branch name`);
        continue;
      }

      try {
        // Find branch by name
        const branch = await Branch.findOne({
          where: {
            name: branchName,
            deleted_at: null,
          },
        });

        if (!branch) {
          errors.push(`Branch not found: ${branchName}`);
          logger.error(`❌ Branch not found: ${branchName}`);
          continue;
        }

        // Find POS sessions for this branch from 27th May 2025
        const targetDate = "2025-05-27";
        const startOfDay = `${targetDate} 00:00:00`;
        const endOfDay = `${targetDate} 23:59:59`;

        const { Op } = require("sequelize");

        const may27Sessions = await PosSession.findAll({
          where: {
            branch_id: branch.id,
            start_time: {
              [Op.between]: [startOfDay, endOfDay],
            },
            deleted_at: null,
          },
          order: [["start_time", "DESC"]],
        });

        if (may27Sessions.length === 0) {
          errors.push(
            `No POS sessions found for branch ${branchName} on 27th May 2025`
          );
          logger.error(
            `❌ No POS sessions found for branch ${branchName} on 27th May 2025`
          );
          continue;
        }

        logger.info(
          `📋 Found ${may27Sessions.length} session(s) for ${branchName} on 27th May`
        );

        // Process each session from 27th May
        for (const posSession of may27Sessions) {
          logger.info(
            `🔍 Processing Session ID: ${posSession.id} (${posSession.start_time})`
          );

          // Find or create reconciliation record
          let reconciliation = await PosSessionReconciliation.findOne({
            where: {
              pos_session_id: posSession.id,
            },
          });

          if (reconciliation) {
            // Update existing reconciliation
            await reconciliation.update(
              {
                closing_cash_balance: cashBalance,
                closing_mpesa_float: floatBalance,
                notes: reconciliation.notes
                  ? `${reconciliation.notes} | Updated from 27th balances.xlsx`
                  : "Updated from 27th balances.xlsx",
              },
              { transaction }
            );

            updates.push({
              branch: branchName,
              sessionId: posSession.id,
              action: "updated",
              cashBalance,
              floatBalance,
            });

            logger.info(
              `✅ Updated reconciliation for ${branchName} Session ${posSession.id} - Cash: ${cashBalance}, Float: ${floatBalance}`
            );
          } else {
            // Create new reconciliation record
            reconciliation = await PosSessionReconciliation.create(
              {
                pos_session_id: posSession.id,
                closing_cash_balance: cashBalance,
                closing_mpesa_balance: 0.0,
                closing_mpesa_float: floatBalance,
                cash_payments: 0.0,
                mpesa_payments: 0.0,
                total_sales: 0.0,
                discrepancies: null,
                total_variance: 0.0,
                total_bankings: 0.0,
                total_dsa_sales: 0.0,
                notes: "Created from 27th balances.xlsx",
                created_by: 1, // System user
              },
              { transaction }
            );

            updates.push({
              branch: branchName,
              sessionId: posSession.id,
              action: "created",
              cashBalance,
              floatBalance,
            });

            logger.info(
              `✅ Created reconciliation for ${branchName} Session ${posSession.id} - Cash: ${cashBalance}, Float: ${floatBalance}`
            );
          }
        } // End of session loop
      } catch (error) {
        errors.push(`Error processing ${branchName}: ${error.message}`);
        logger.error(`❌ Error processing ${branchName}: ${error.message}`);
      }
    } // End of balance entry loop

    // Commit transaction
    await transaction.commit();

    // Summary report
    logger.info("\n📊 CLOSING BALANCES UPDATE SUMMARY");
    logger.info("=====================================");
    logger.info(`✅ Successfully processed: ${updates.length} branches`);
    logger.info(`❌ Errors encountered: ${errors.length}`);

    if (updates.length > 0) {
      logger.info("\n📋 Successful Updates:");
      updates.forEach((update) => {
        logger.info(
          `  • ${update.branch} (Session ${update.sessionId}) - ${update.action} - Cash: ${update.cashBalance}, Float: ${update.floatBalance}`
        );
      });
    }

    if (errors.length > 0) {
      logger.info("\n⚠️ Errors:");
      errors.forEach((error) => {
        logger.error(`  • ${error}`);
      });
    }

    logger.info("\n🎉 Closing balances update completed successfully!");
  } catch (error) {
    await transaction.rollback();
    logger.error("💥 Fatal error during closing balances update:", error);
    throw error;
  }
}

// Execute the script
if (require.main === module) {
  updateClosingBalances()
    .then(() => {
      logger.info("✅ Script execution completed");
      process.exit(0);
    })
    .catch((error) => {
      logger.error("💥 Script execution failed:", error);
      process.exit(1);
    });
}

module.exports = { updateClosingBalances };
