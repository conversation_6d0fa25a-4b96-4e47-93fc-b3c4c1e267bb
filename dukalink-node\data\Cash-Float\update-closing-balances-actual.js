const path = require("path");
const {
  Branch,
  PosSession,
  PosSessionReconciliation,
  User,
} = require("../../src/models");
const sequelize = require("../../config/database");
const logger = require("../../src/utils/logger");

/**
 * ACTUAL UPDATE SCRIPT - MAKES REAL DATABASE CHANGES
 * 
 * This script implements the changes previewed in preview-with-session-creation.js:
 * 1. Creates missing POS sessions for branches without sessions on 27th May
 * 2. Updates existing reconciliation records with new closing balances
 * 3. Creates new reconciliation records for sessions without reconciliations
 * 
 * ⚠️ WARNING: This script makes REAL database changes!
 * Always run the preview script first to validate changes.
 */

// Excel data from 27th balances.xlsx with corrected branch names
const balancesData = [
  { branch: "Bungoma", cash: 13449, float: 10257 },
  { branch: "Busia", cash: 17363, float: 14519 },
  { branch: "Chu<PERSON>", cash: 21801, float: 1928 },
  { branch: "<PERSON><PERSON>", cash: 16150, float: 55958 },
  { branch: "Homa<PERSON>", cash: 70312, float: 4445 },
  { branch: "Kapenguria", cash: 13325, float: 4161 },
  { branch: "Kapsabet", cash: 5270, float: 13395 },
  { branch: "Karatina", cash: 5425, float: 2073 },
  { branch: "Kathonzweni", cash: 29304, float: 935 },
  { branch: "Kehancha", cash: 750, float: 20832 },
  { branch: "Kericho", cash: 20040, float: 3709 },
  { branch: "Kerugoya", cash: 23390, float: 3792 },
  { branch: "Kibwezi", cash: 16430, float: 17145 },
  { branch: "Kikima", cash: 7985, float: 80133 },
  { branch: "Kilgoris", cash: 30923, float: 11159 },
  { branch: "Kilifi", cash: 7275, float: 26675 },
  { branch: "Kisumu", cash: 72153, float: 43634 },
  { branch: "Kitale 2", cash: 24014, float: 14414 },
  { branch: "Kitui", cash: 21014, float: 2016 },
  { branch: "Likoni", cash: 34535, float: 17243 },
  { branch: "Machakos", cash: 28009, float: 13725 },
  { branch: "Magunga", cash: 9805, float: 26438 },
  { branch: "Malaba", cash: 21463, float: 24168 },
  { branch: "Mariakani 2", cash: 4748, float: 19750 },
  { branch: "Mau summit", cash: 34440, float: 9560 },
  { branch: "Maua", cash: 14920, float: 16424 },
  { branch: "Mbale", cash: 19145, float: 68177 },
  { branch: "Mbita", cash: 28038, float: 624 },
  { branch: "Mombasa ", cash: 34821, float: 96381 },
  { branch: "Mtwapa", cash: 5696, float: 54296 },
  { branch: "Muhuru Bay", cash: 18140, float: 15260 },
  { branch: "Mumias", cash: 3572, float: 11060 },
  { branch: "Mutomo", cash: 10365, float: 29463 },
  { branch: "Mwatate", cash: 12668, float: 9057 },
  { branch: "Mwingi", cash: 4304, float: 15261 },
  { branch: "Nkubu", cash: 18900, float: 656 },
  { branch: "Nyamira", cash: 40006, float: 1896 },
  { branch: "Nyeri", cash: 28769, float: 20628 },
  { branch: "Oyugis", cash: 12518, float: 13794 },
  { branch: "Prudential", cash: 54726, float: 10102 },
  { branch: "Sori", cash: 32, float: 2599 },
  { branch: "Webuye", cash: 39962, float: 17681 },
  { branch: "Wote", cash: 16041, float: 5890 },
  { branch: "Wundanyi", cash: 9830, float: 12458 }
];

async function updateClosingBalancesActual() {
  const transaction = await sequelize.transaction();
  
  try {
    logger.info('🚀 STARTING ACTUAL CLOSING BALANCES UPDATE');
    logger.info('==========================================');
    logger.info('⚠️  THIS WILL MAKE REAL DATABASE CHANGES!');
    logger.info('📅 Processing 27th May 2025 balances...');
    
    const results = {
      sessionsCreated: [],
      reconciliationsUpdated: [],
      reconciliationsCreated: [],
      errors: [],
      warnings: []
    };
    
    // Step 1: Find system user for creating sessions
    logger.info('\n📋 Step 1: Finding system user for session creation...');
    
    const systemUser = await User.findOne({
      where: {
        role_id: 11, // company_admin
        deleted_at: null
      },
      transaction
    });
    
    if (!systemUser) {
      throw new Error('No company_admin user found for creating system sessions');
    }
    
    logger.info(`✅ Using system user: ${systemUser.name} (ID: ${systemUser.id})`);
    
    // Step 2: Get all existing sessions from 27th May
    logger.info('\n📋 Step 2: Loading existing POS sessions from 27th May 2025...');
    
    const targetDate = '2025-05-27';
    const startOfDay = `${targetDate} 00:00:00`;
    const endOfDay = `${targetDate} 23:59:59`;
    
    const { Op } = require('sequelize');
    
    const may27Sessions = await PosSession.findAll({
      where: {
        start_time: {
          [Op.between]: [startOfDay, endOfDay]
        },
        deleted_at: null
      },
      include: [
        {
          model: Branch,
          attributes: ['id', 'name', 'location']
        },
        {
          model: PosSessionReconciliation,
          as: 'reconciliation',
          required: false
        }
      ],
      order: [['start_time', 'ASC']],
      transaction
    });
    
    logger.info(`📊 Found ${may27Sessions.length} existing POS sessions from 27th May 2025`);
    
    // Step 3: Process each balance entry
    logger.info('\n📋 Step 3: Processing balance updates and session creations...');
    
    for (const balanceEntry of balancesData) {
      const { branch: branchName, cash: newCashBalance, float: newFloatBalance } = balanceEntry;
      
      logger.info(`\n🏪 Processing: ${branchName}`);
      logger.info(`   💰 Target Cash: ${newCashBalance.toLocaleString()}`);
      logger.info(`   📱 Target Float: ${newFloatBalance.toLocaleString()}`);
      
      try {
        // Find branch
        const branch = await Branch.findOne({
          where: { 
            name: branchName,
            deleted_at: null 
          },
          transaction
        });
        
        if (!branch) {
          results.errors.push(`Branch not found: ${branchName}`);
          logger.error(`   ❌ Branch not found: ${branchName}`);
          continue;
        }
        
        // Find sessions for this branch on 27th May
        const branchSessions = may27Sessions.filter(session => 
          session.Branch && session.Branch.name === branchName
        );
        
        if (branchSessions.length === 0) {
          // No sessions found - create one
          logger.info(`   🏗️ Creating new POS session for ${branchName}...`);
          
          const sessionStartTime = new Date(`${targetDate}T08:00:00.000Z`);
          const sessionEndTime = new Date(`${targetDate}T20:00:00.000Z`);
          
          // Create POS session
          const newSession = await PosSession.create({
            user_id: systemUser.id,
            branch_id: branch.id,
            start_time: sessionStartTime,
            end_time: sessionEndTime,
            opening_cash_balance: 1000.00,
            opening_mpesa_float: 1000.00,
            status: 'closed',
            notes: 'System-created session for 27th May balances import',
            created_by: systemUser.id
          }, { transaction });
          
          logger.info(`   ✅ Created POS Session ID: ${newSession.id}`);
          
          // Create reconciliation for the new session
          const newReconciliation = await PosSessionReconciliation.create({
            pos_session_id: newSession.id,
            closing_cash_balance: newCashBalance,
            closing_mpesa_balance: 0.00,
            closing_mpesa_float: newFloatBalance,
            cash_payments: 0.00,
            mpesa_payments: 0.00,
            total_sales: 0.00,
            discrepancies: null,
            total_variance: 0.00,
            total_bankings: 0.00,
            total_dsa_sales: 0.00,
            notes: 'Created from 27th balances.xlsx import',
            created_by: systemUser.id
          }, { transaction });
          
          logger.info(`   ✅ Created Reconciliation ID: ${newReconciliation.id}`);
          
          results.sessionsCreated.push({
            branchName,
            sessionId: newSession.id,
            reconciliationId: newReconciliation.id,
            cashBalance: newCashBalance,
            floatBalance: newFloatBalance
          });
          
          continue;
        }
        
        logger.info(`   📋 Processing ${branchSessions.length} existing session(s):`);
        
        // Process each existing session
        for (const session of branchSessions) {
          logger.info(`\n   🔍 Session ID: ${session.id} (${session.start_time})`);
          
          const existingReconciliation = session.reconciliation;
          
          if (existingReconciliation) {
            // Update existing reconciliation
            logger.info(`      🔄 Updating existing reconciliation ID: ${existingReconciliation.id}`);
            
            const oldCash = existingReconciliation.closing_cash_balance;
            const oldFloat = existingReconciliation.closing_mpesa_float;
            
            await existingReconciliation.update({
              closing_cash_balance: newCashBalance,
              closing_mpesa_float: newFloatBalance,
              notes: existingReconciliation.notes ? 
                `${existingReconciliation.notes} | Updated from 27th balances.xlsx on ${new Date().toISOString()}` : 
                `Updated from 27th balances.xlsx on ${new Date().toISOString()}`
            }, { transaction });
            
            logger.info(`      ✅ Updated: Cash ${oldCash} → ${newCashBalance}, Float ${oldFloat} → ${newFloatBalance}`);
            
            results.reconciliationsUpdated.push({
              branchName,
              sessionId: session.id,
              reconciliationId: existingReconciliation.id,
              oldCash,
              newCash: newCashBalance,
              oldFloat,
              newFloat: newFloatBalance
            });
            
          } else {
            // Create new reconciliation
            logger.info(`      ➕ Creating new reconciliation for session ${session.id}`);
            
            const newReconciliation = await PosSessionReconciliation.create({
              pos_session_id: session.id,
              closing_cash_balance: newCashBalance,
              closing_mpesa_balance: 0.00,
              closing_mpesa_float: newFloatBalance,
              cash_payments: 0.00,
              mpesa_payments: 0.00,
              total_sales: 0.00,
              discrepancies: null,
              total_variance: 0.00,
              total_bankings: 0.00,
              total_dsa_sales: 0.00,
              notes: 'Created from 27th balances.xlsx import',
              created_by: systemUser.id
            }, { transaction });
            
            logger.info(`      ✅ Created reconciliation ID: ${newReconciliation.id}`);
            
            results.reconciliationsCreated.push({
              branchName,
              sessionId: session.id,
              reconciliationId: newReconciliation.id,
              cashBalance: newCashBalance,
              floatBalance: newFloatBalance
            });
          }
        }
        
      } catch (error) {
        results.errors.push(`Error processing ${branchName}: ${error.message}`);
        logger.error(`   ❌ Error processing ${branchName}: ${error.message}`);
      }
    }
    
    // Commit transaction
    await transaction.commit();
    
    // Final Summary Report
    logger.info('\n🎉 CLOSING BALANCES UPDATE COMPLETED SUCCESSFULLY!');
    logger.info('==================================================');
    logger.info(`🏗️ POS Sessions Created: ${results.sessionsCreated.length}`);
    logger.info(`🔄 Reconciliations Updated: ${results.reconciliationsUpdated.length}`);
    logger.info(`➕ Reconciliations Created: ${results.reconciliationsCreated.length}`);
    logger.info(`⚠️ Warnings: ${results.warnings.length}`);
    logger.info(`❌ Errors: ${results.errors.length}`);
    
    if (results.sessionsCreated.length > 0) {
      logger.info('\n🏗️ NEW SESSIONS CREATED:');
      results.sessionsCreated.forEach((creation, index) => {
        logger.info(`${index + 1}. ${creation.branchName} - Session ${creation.sessionId}, Reconciliation ${creation.reconciliationId}`);
      });
    }
    
    if (results.reconciliationsUpdated.length > 0) {
      logger.info('\n🔄 RECONCILIATIONS UPDATED:');
      results.reconciliationsUpdated.forEach((update, index) => {
        logger.info(`${index + 1}. ${update.branchName} - Session ${update.sessionId} - Cash: ${update.oldCash} → ${update.newCash}, Float: ${update.oldFloat} → ${update.newFloat}`);
      });
    }
    
    if (results.reconciliationsCreated.length > 0) {
      logger.info('\n➕ RECONCILIATIONS CREATED:');
      results.reconciliationsCreated.forEach((creation, index) => {
        logger.info(`${index + 1}. ${creation.branchName} - Session ${creation.sessionId} - Cash: ${creation.cashBalance}, Float: ${creation.floatBalance}`);
      });
    }
    
    if (results.errors.length > 0) {
      logger.info('\n❌ ERRORS:');
      results.errors.forEach(error => logger.error(`  • ${error}`));
    }
    
    const totalChanges = results.sessionsCreated.length + results.reconciliationsUpdated.length + results.reconciliationsCreated.length;
    logger.info(`\n📊 TOTAL CHANGES MADE: ${totalChanges}`);
    logger.info('💾 All changes have been committed to the database.');
    
    return results;
    
  } catch (error) {
    await transaction.rollback();
    logger.error('💥 FATAL ERROR - All changes have been rolled back:', error);
    throw error;
  }
}

// Execute the actual update
if (require.main === module) {
  updateClosingBalancesActual()
    .then((results) => {
      logger.info('✅ Actual update execution completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('💥 Actual update execution failed:', error);
      process.exit(1);
    });
}

module.exports = { updateClosingBalancesActual };
