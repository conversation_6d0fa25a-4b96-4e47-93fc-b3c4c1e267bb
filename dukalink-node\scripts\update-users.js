/**
 * Update Users Script
 * Updates user information based on provided array
 */

const { User, Role } = require("../src/models");
const bcrypt = require("bcryptjs");
const logger = require("../src/utils/logger");
const sequelize = require("../config/database");

/**
 * Array of users to update with their new information
 */
const usersToUpdate = [
  {
    currentEmail: "<EMAIL>",
    newEmail: "<EMAIL>",
    password: "admin123",
    name: "<PERSON><PERSON><PERSON>",
    role: "company_admin",
  },
  {
    currentEmail: "<EMAIL>",
    newEmail: "<EMAIL>", // No change
    password: "admin123",
    name: "<PERSON><PERSON>bu",
    role: "company_admin",
  },
  {
    currentEmail: "<EMAIL>",
    newEmail: "<EMAIL>",
    password: "finance123",
    name: "Accounts Department",
    role: "accountant",
  },
  {
    currentEmail: "<EMAIL>",
    newEmail: "<EMAIL>",
    password: "finance123",
    name: "Sushma",
    role: "finance_manager",
  },
  {
    currentEmail: "<EMAIL>",
    newEmail: "<EMAIL>",
    password: "audit123",
    name: "Jane Mucheru",
    role: "auditor",
  },
  {
    currentEmail: "<EMAIL>",
    newEmail: "<EMAIL>",
    password: "stock123",
    name: "Stores Department",
    role: "stock_admin",
  },
  {
    currentEmail: "<EMAIL>",
    newEmail: "<EMAIL>",
    password: "ops123",
    name: "John Ndegwa",
    role: "operations_manager",
  },
  {
    currentEmail: "<EMAIL>",
    newEmail: "<EMAIL>",
    password: "float123",
    name: "Mpesa Department",
    role: "float_manager",
  },
];

/**
 * Hash password using bcrypt
 */
const hashPassword = async (password) => {
  const saltRounds = 10;
  return await bcrypt.hash(password, saltRounds);
};

/**
 * Find role by name
 */
const findRoleByName = async (roleName) => {
  const role = await Role.findOne({
    where: { name: roleName },
  });

  if (!role) {
    throw new Error(`Role '${roleName}' not found`);
  }

  return role;
};

/**
 * Update a single user
 */
const updateUser = async (userInfo, transaction) => {
  const { currentEmail, newEmail, password, name, role: roleName } = userInfo;

  try {
    // Find the user by current email
    const user = await User.findOne({
      where: { email: currentEmail },
      transaction,
    });

    if (!user) {
      logger.warn(`User with email '${currentEmail}' not found. Skipping.`);
      return { success: false, message: `User '${currentEmail}' not found` };
    }

    // Check if the new email already exists (and it's not the same user)
    if (currentEmail !== newEmail) {
      const existingUser = await User.findOne({
        where: { email: newEmail },
        transaction,
      });

      if (existingUser && existingUser.id !== user.id) {
        logger.warn(
          `Email '${newEmail}' already exists for another user. Skipping email update for '${currentEmail}'.`
        );
        // Update everything except email
        const role = await findRoleByName(roleName);
        await user.update(
          {
            password: password,
            name: name,
            role_id: role.id,
          },
          { transaction }
        );

        return {
          success: true,
          message: `Updated user '${currentEmail}' (kept original email due to conflict)`,
          oldEmail: currentEmail,
          newEmail: currentEmail, // Keep original email
          name: name,
          role: roleName,
        };
      }
    }

    // Find the role
    const role = await findRoleByName(roleName);

    // Update the user (password will be automatically hashed by model hooks)
    await user.update(
      {
        email: newEmail,
        password: password,
        name: name,
        role_id: role.id,
      },
      { transaction }
    );

    logger.info(`✅ Updated user: ${currentEmail} → ${newEmail} (${name})`);
    return {
      success: true,
      message: `Updated user: ${currentEmail} → ${newEmail}`,
      oldEmail: currentEmail,
      newEmail: newEmail,
      name: name,
      role: roleName,
    };
  } catch (error) {
    logger.error(`❌ Error updating user '${currentEmail}': ${error.message}`);
    return {
      success: false,
      message: `Error updating user '${currentEmail}': ${error.message}`,
      oldEmail: currentEmail,
    };
  }
};

/**
 * Main function to update all users
 */
const updateUsers = async () => {
  const transaction = await sequelize.transaction();

  try {
    logger.info("🚀 Starting user update process...");
    logger.info(`📊 Total users to update: ${usersToUpdate.length}`);

    const results = {
      successful: 0,
      failed: 0,
      details: [],
    };

    // Process each user
    for (const userInfo of usersToUpdate) {
      const result = await updateUser(userInfo, transaction);
      results.details.push(result);

      if (result.success) {
        results.successful++;
      } else {
        results.failed++;
      }
    }

    // Commit the transaction
    await transaction.commit();

    // Display results
    logger.info("\n📋 UPDATE SUMMARY:");
    logger.info(`✅ Successful updates: ${results.successful}`);
    logger.info(`❌ Failed updates: ${results.failed}`);
    logger.info(`📊 Total processed: ${usersToUpdate.length}`);

    // Display detailed results
    logger.info("\n📝 DETAILED RESULTS:");
    results.details.forEach((result, index) => {
      const status = result.success ? "✅" : "❌";
      logger.info(`${status} ${index + 1}. ${result.message}`);
    });

    // Display failed updates if any
    const failedUpdates = results.details.filter((r) => !r.success);
    if (failedUpdates.length > 0) {
      logger.info("\n⚠️  FAILED UPDATES:");
      failedUpdates.forEach((result, index) => {
        logger.error(`${index + 1}. ${result.oldEmail}: ${result.message}`);
      });
    }

    logger.info("\n🎉 User update process completed!");
  } catch (error) {
    await transaction.rollback();
    logger.error(`💥 Fatal error during user update: ${error.message}`);
    logger.error(`Stack trace: ${error.stack}`);
    throw error;
  }
};

/**
 * Run the script
 */
const runScript = async () => {
  try {
    // Test database connection
    await sequelize.authenticate();
    logger.info("✅ Database connection established");

    // Run the update
    await updateUsers();

    // Close database connection
    await sequelize.close();
    logger.info("✅ Database connection closed");

    process.exit(0);
  } catch (error) {
    logger.error(`💥 Script failed: ${error.message}`);
    process.exit(1);
  }
};

// Run the script if called directly
if (require.main === module) {
  runScript();
}

module.exports = {
  updateUsers,
  usersToUpdate,
};
