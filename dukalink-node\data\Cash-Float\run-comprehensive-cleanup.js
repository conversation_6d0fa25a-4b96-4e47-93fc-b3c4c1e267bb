#!/usr/bin/env node

/**
 * COMPREHENSIVE DUPLICATE RECONCILIATION CLEANUP RUNNER
 * 
 * This script provides an interactive interface for cleaning up duplicate reconciliations
 * found in the comprehensive discrepancy check.
 * 
 * MODES:
 * - PREVIEW: Shows what would be cleaned up (NO DATABASE CHANGES)
 * - EXECUTE: Actually performs the cleanup (MODIFIES DATABASE)
 * 
 * Usage:
 * - node data/cash-float/run-comprehensive-cleanup.js preview
 * - node data/cash-float/run-comprehensive-cleanup.js execute
 */

const path = require('path');
const readline = require('readline');

// Set up environment
require('dotenv').config({ path: path.join(__dirname, '../../.env') });

// Import the cleanup function
const { comprehensiveCleanupDuplicates } = require('./comprehensive-cleanup-duplicates');

// Create readline interface for user confirmation
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer);
    });
  });
}

async function main() {
  const mode = process.argv[2]?.toUpperCase() || 'PREVIEW';
  
  if (!['PREVIEW', 'EXECUTE'].includes(mode)) {
    console.log('❌ Invalid mode. Usage:');
    console.log('   node data/cash-float/run-comprehensive-cleanup.js preview');
    console.log('   node data/cash-float/run-comprehensive-cleanup.js execute');
    process.exit(1);
  }
  
  console.log('🔧 COMPREHENSIVE DUPLICATE RECONCILIATION CLEANUP');
  console.log('==================================================');
  
  if (mode === 'PREVIEW') {
    console.log('👁️ PREVIEW MODE - NO DATABASE CHANGES');
    console.log('📋 This will show what would be cleaned up');
  } else {
    console.log('🚨 EXECUTE MODE - ACTUAL DATABASE CHANGES');
    console.log('⚠️ WARNING: This will modify the production database!');
  }
  
  console.log('\n🎯 TARGET ISSUES:');
  console.log('   • Homabay (Session 149): 2 reconciliations');
  console.log('   • Malaba (Session 133): 2 reconciliations');
  console.log('   • Webuye (Session 162): 2 reconciliations');
  console.log('   • Bungoma (Session 131): 2 reconciliations');
  console.log('   • Karatina (Session 110): 2 reconciliations');
  console.log('   • Magunga (Session 157): 2 reconciliations');
  
  console.log('\n✅ STRATEGY:');
  console.log('   • Keep reconciliations that match Excel values');
  console.log('   • Remove reconciliations created by mobile app with wrong values');
  console.log('   • Ensure each session has exactly one reconciliation');
  
  if (mode === 'EXECUTE') {
    console.log('\n⚠️ PRODUCTION DATABASE WARNING:');
    console.log('   • This will permanently delete incorrect reconciliations');
    console.log('   • Ensure you have recent database backups');
    console.log('   • Run PREVIEW mode first to verify the changes');
    
    const answer = await askQuestion('\n❓ Do you want to proceed with EXECUTE mode? (yes/no): ');
    
    if (answer.toLowerCase() !== 'yes' && answer.toLowerCase() !== 'y') {
      console.log('\n❌ Cleanup cancelled by user.');
      rl.close();
      process.exit(0);
    }
    
    const confirmAnswer = await askQuestion('\n❓ Are you absolutely sure? This will modify production data (yes/no): ');
    
    if (confirmAnswer.toLowerCase() !== 'yes' && confirmAnswer.toLowerCase() !== 'y') {
      console.log('\n❌ Cleanup cancelled by user.');
      rl.close();
      process.exit(0);
    }
  }
  
  console.log(`\n🚀 Starting comprehensive cleanup in ${mode} mode...`);
  console.log('⏳ This may take a few moments...\n');
  
  try {
    const results = await comprehensiveCleanupDuplicates(mode);
    
    console.log(`\n✅ Comprehensive cleanup ${mode.toLowerCase()} completed successfully!`);
    
    console.log('\n📊 FINAL SUMMARY:');
    console.log(`   • Sessions analyzed: ${results.sessionsAnalyzed}`);
    console.log(`   • Sessions with duplicates: ${results.duplicatesFound}`);
    console.log(`   • Correct reconciliations kept: ${results.correctReconciliations}`);
    console.log(`   • Incorrect reconciliations ${mode === 'PREVIEW' ? 'to remove' : 'removed'}: ${results.incorrectReconciliations}`);
    console.log(`   • Errors encountered: ${results.errors.length}`);
    
    if (mode === 'PREVIEW') {
      console.log('\n👁️ PREVIEW RESULTS:');
      if (results.reconciliationsToDelete.length > 0) {
        console.log(`   • ${results.reconciliationsToDelete.length} reconciliations would be deleted`);
        console.log('   • All deletions are for reconciliations with wrong values');
        console.log('   • Reconciliations matching Excel values will be kept');
        
        console.log('\n🔧 NEXT STEPS:');
        console.log('   1. Review the detailed logs above');
        console.log('   2. Verify the reconciliations to be deleted are correct');
        console.log('   3. Run in EXECUTE mode when ready:');
        console.log('      node data/cash-float/run-comprehensive-cleanup.js execute');
      } else {
        console.log('   ✅ No cleanup needed - all data is already clean!');
        console.log('   🚀 You can proceed directly with final balance updates');
      }
    } else {
      console.log('\n🎉 CLEANUP COMPLETED SUCCESSFULLY!');
      if (results.reconciliationsToDelete.length > 0) {
        console.log(`   ✅ Removed ${results.reconciliationsToDelete.length} incorrect reconciliations`);
        console.log('   ✅ Kept reconciliations that match Excel values');
        console.log('   ✅ Each session now has exactly one reconciliation');
        
        console.log('\n🔍 VERIFICATION RECOMMENDED:');
        console.log('   1. Run comprehensive discrepancy check again:');
        console.log('      node data/cash-float/run-comprehensive-check.js');
        console.log('   2. Verify no duplicate reconciliations remain');
        console.log('   3. Proceed with final balance updates when clean');
      } else {
        console.log('   ✅ No changes were needed - data was already clean');
      }
    }
    
    if (results.errors.length > 0) {
      console.log('\n⚠️ ERRORS ENCOUNTERED:');
      results.errors.forEach(error => {
        console.log(`   • ${error}`);
      });
      console.log('\n🔧 Please review and resolve errors before proceeding');
    }
    
  } catch (error) {
    console.error(`\n💥 Cleanup ${mode.toLowerCase()} failed:`, error.message);
    
    if (mode === 'EXECUTE') {
      console.error('🔄 All changes have been rolled back');
      console.error('📋 Database remains unchanged');
    }
    
    if (error.stack) {
      console.error('\n📝 Error details:');
      console.error(error.stack);
    }
    
    rl.close();
    process.exit(1);
  }
  
  rl.close();
  process.exit(0);
}

// Run the main function
main().catch((error) => {
  console.error('💥 Fatal error:', error);
  rl.close();
  process.exit(1);
});
