const path = require("path");
const {
  Branch,
  PosSession,
  PosSessionReconciliation,
} = require("../../src/models");
const sequelize = require("../../config/database");
const logger = require("../../src/utils/logger");

/**
 * PREVIEW SCRIPT - NO DATABASE CHANGES
 * 
 * This script simulates updating closing balances for POS sessions from 27th May
 * Shows exactly what would be changed without making any actual database modifications
 * 
 * Database Analysis:
 * - Finds all POS sessions from 27th May 2025 (not just recent ones)
 * - Shows current vs proposed values for each reconciliation
 * - Validates branch names and session availability
 * - Provides detailed logging for each step
 */

// Excel data from 27th balances.xlsx with corrected branch names to match database
const balancesData = [
  { branch: "Bungoma", cash: 13449, float: 10257 },
  { branch: "Busia", cash: 17363, float: 14519 },
  { branch: "Chuka", cash: 21801, float: 1928 },
  { branch: "<PERSON><PERSON>", cash: 16150, float: 55958 },
  { branch: "Homa<PERSON>", cash: 70312, float: 4445 },
  { branch: "Kapenguria", cash: 13325, float: 4161 },
  { branch: "Kapsabet", cash: 5270, float: 13395 },
  { branch: "Karatina", cash: 5425, float: 2073 },
  { branch: "Kathonzweni", cash: 29304, float: 935 },
  { branch: "Kehancha", cash: 750, float: 20832 },
  { branch: "Kericho", cash: 20040, float: 3709 },
  { branch: "Kerugoya", cash: 23390, float: 3792 },
  { branch: "Kibwezi", cash: 16430, float: 17145 },
  { branch: "Kikima", cash: 7985, float: 80133 },
  { branch: "Kilgoris", cash: 30923, float: 11159 },
  { branch: "Kilifi", cash: 7275, float: 26675 },
  { branch: "Kisumu", cash: 72153, float: 43634 },
  { branch: "Kitale 2", cash: 24014, float: 14414 },
  { branch: "Kitui", cash: 21014, float: 2016 },
  { branch: "Likoni", cash: 34535, float: 17243 },
  { branch: "Machakos", cash: 28009, float: 13725 },
  { branch: "Magunga", cash: 9805, float: 26438 },
  { branch: "Malaba", cash: 21463, float: 24168 },
  { branch: "Mariakani 2", cash: 4748, float: 19750 },
  { branch: "Mau summit", cash: 34440, float: 9560 },
  { branch: "Maua", cash: 14920, float: 16424 },
  { branch: "Mbale", cash: 19145, float: 68177 },
  { branch: "Mbita", cash: 28038, float: 624 },
  { branch: "Mombasa ", cash: 34821, float: 96381 },
  { branch: "Mtwapa", cash: 5696, float: 54296 },
  { branch: "Muhuru Bay", cash: 18140, float: 15260 },
  { branch: "Mumias", cash: 3572, float: 11060 },
  { branch: "Mutomo", cash: 10365, float: 29463 },
  { branch: "Mwatate", cash: 12668, float: 9057 },
  { branch: "Mwingi", cash: 4304, float: 15261 },
  { branch: "Nkubu", cash: 18900, float: 656 },
  { branch: "Nyamira", cash: 40006, float: 1896 },
  { branch: "Nyeri", cash: 28769, float: 20628 },
  { branch: "Oyugis", cash: 12518, float: 13794 },
  { branch: "Prudential", cash: 54726, float: 10102 },
  { branch: "Sori", cash: 32, float: 2599 },
  { branch: "Webuye", cash: 39962, float: 17681 },
  { branch: "Wote", cash: 16041, float: 5890 },
  { branch: "Wundanyi", cash: 9830, float: 12458 }
];

async function previewClosingBalancesUpdate() {
  try {
    logger.info('🔍 PREVIEW MODE - NO DATABASE CHANGES WILL BE MADE');
    logger.info('=====================================================');
    logger.info('📅 Looking for POS sessions from 27th May 2025...');
    
    const previewResults = [];
    const errors = [];
    const warnings = [];
    
    // Step 1: Validate all branches exist
    logger.info('\n📋 Step 1: Validating branch names from Excel data...');
    const branchValidation = [];
    
    for (const balanceEntry of balancesData) {
      const { branch: branchName } = balanceEntry;
      
      logger.info(`🔍 Checking branch: "${branchName}"`);
      
      const branch = await Branch.findOne({
        where: { 
          name: branchName,
          deleted_at: null 
        }
      });
      
      if (branch) {
        branchValidation.push({
          branchName,
          branchId: branch.id,
          status: 'FOUND',
          location: branch.location
        });
        logger.info(`  ✅ Found: ID ${branch.id}, Location: ${branch.location}`);
      } else {
        branchValidation.push({
          branchName,
          branchId: null,
          status: 'NOT_FOUND'
        });
        errors.push(`Branch not found: "${branchName}"`);
        logger.error(`  ❌ Not found: "${branchName}"`);
      }
    }
    
    // Step 2: Find all POS sessions from 27th May 2025
    logger.info('\n📋 Step 2: Finding POS sessions from 27th May 2025...');
    
    const targetDate = '2025-05-27';
    const startOfDay = `${targetDate} 00:00:00`;
    const endOfDay = `${targetDate} 23:59:59`;
    
    logger.info(`🗓️ Searching for sessions between ${startOfDay} and ${endOfDay}`);
    
    const { Op } = require('sequelize');
    
    const may27Sessions = await PosSession.findAll({
      where: {
        start_time: {
          [Op.between]: [startOfDay, endOfDay]
        },
        deleted_at: null
      },
      include: [
        {
          model: Branch,
          attributes: ['id', 'name', 'location']
        },
        {
          model: PosSessionReconciliation,
          as: 'reconciliation',
          required: false
        }
      ],
      order: [['start_time', 'ASC']]
    });
    
    logger.info(`📊 Found ${may27Sessions.length} POS sessions from 27th May 2025`);
    
    // Step 3: Process each balance entry and show what would change
    logger.info('\n📋 Step 3: Analyzing proposed changes...');
    
    for (const balanceEntry of balancesData) {
      const { branch: branchName, cash: newCashBalance, float: newFloatBalance } = balanceEntry;
      
      logger.info(`\n🏪 Processing: ${branchName}`);
      logger.info(`   💰 Proposed Cash: ${newCashBalance.toLocaleString()}`);
      logger.info(`   📱 Proposed Float: ${newFloatBalance.toLocaleString()}`);
      
      // Find branch
      const branchInfo = branchValidation.find(b => b.branchName === branchName);
      if (!branchInfo || branchInfo.status !== 'FOUND') {
        logger.error(`   ❌ Skipping - Branch not found`);
        continue;
      }
      
      // Find sessions for this branch on 27th May
      const branchSessions = may27Sessions.filter(session => 
        session.Branch && session.Branch.name === branchName
      );
      
      if (branchSessions.length === 0) {
        warnings.push(`No sessions found for ${branchName} on 27th May 2025`);
        logger.warn(`   ⚠️ No sessions found for this branch on 27th May`);
        continue;
      }
      
      logger.info(`   📋 Found ${branchSessions.length} session(s) for this branch on 27th May:`);
      
      // Process each session
      for (const session of branchSessions) {
        logger.info(`\n   🔍 Session ID: ${session.id}`);
        logger.info(`      ⏰ Start Time: ${session.start_time}`);
        logger.info(`      📊 Status: ${session.status}`);
        
        const existingReconciliation = session.reconciliation;
        
        if (existingReconciliation) {
          logger.info(`      📝 Existing Reconciliation ID: ${existingReconciliation.id}`);
          logger.info(`         💰 Current Cash: ${existingReconciliation.closing_cash_balance || 'NULL'}`);
          logger.info(`         📱 Current Float: ${existingReconciliation.closing_mpesa_float || 'NULL'}`);
          logger.info(`      🔄 WOULD UPDATE:`);
          logger.info(`         💰 Cash: ${existingReconciliation.closing_cash_balance || 'NULL'} → ${newCashBalance}`);
          logger.info(`         📱 Float: ${existingReconciliation.closing_mpesa_float || 'NULL'} → ${newFloatBalance}`);
          
          previewResults.push({
            action: 'UPDATE',
            branchName,
            sessionId: session.id,
            reconciliationId: existingReconciliation.id,
            currentCash: existingReconciliation.closing_cash_balance,
            newCash: newCashBalance,
            currentFloat: existingReconciliation.closing_mpesa_float,
            newFloat: newFloatBalance,
            sessionStartTime: session.start_time,
            sessionStatus: session.status
          });
        } else {
          logger.info(`      📝 No existing reconciliation`);
          logger.info(`      ➕ WOULD CREATE NEW RECONCILIATION:`);
          logger.info(`         💰 Cash: ${newCashBalance}`);
          logger.info(`         📱 Float: ${newFloatBalance}`);
          
          previewResults.push({
            action: 'CREATE',
            branchName,
            sessionId: session.id,
            reconciliationId: null,
            currentCash: null,
            newCash: newCashBalance,
            currentFloat: null,
            newFloat: newFloatBalance,
            sessionStartTime: session.start_time,
            sessionStatus: session.status
          });
        }
      }
    }
    
    // Summary Report
    logger.info('\n📊 PREVIEW SUMMARY REPORT');
    logger.info('==========================');
    logger.info(`✅ Branches validated: ${branchValidation.filter(b => b.status === 'FOUND').length}/${branchValidation.length}`);
    logger.info(`📅 Total 27th May sessions found: ${may27Sessions.length}`);
    logger.info(`🔄 Reconciliations to UPDATE: ${previewResults.filter(r => r.action === 'UPDATE').length}`);
    logger.info(`➕ Reconciliations to CREATE: ${previewResults.filter(r => r.action === 'CREATE').length}`);
    logger.info(`⚠️ Warnings: ${warnings.length}`);
    logger.info(`❌ Errors: ${errors.length}`);
    
    if (warnings.length > 0) {
      logger.info('\n⚠️ WARNINGS:');
      warnings.forEach(warning => logger.warn(`  • ${warning}`));
    }
    
    if (errors.length > 0) {
      logger.info('\n❌ ERRORS:');
      errors.forEach(error => logger.error(`  • ${error}`));
    }
    
    if (previewResults.length > 0) {
      logger.info('\n📋 DETAILED CHANGES PREVIEW:');
      previewResults.forEach((result, index) => {
        logger.info(`\n${index + 1}. ${result.action} - ${result.branchName}`);
        logger.info(`   Session: ${result.sessionId} (${result.sessionStartTime})`);
        if (result.action === 'UPDATE') {
          logger.info(`   Cash: ${result.currentCash} → ${result.newCash}`);
          logger.info(`   Float: ${result.currentFloat} → ${result.newFloat}`);
        } else {
          logger.info(`   Cash: ${result.newCash} (new)`);
          logger.info(`   Float: ${result.newFloat} (new)`);
        }
      });
    }
    
    logger.info('\n🎉 Preview completed successfully!');
    logger.info('💡 Review the changes above before running the actual update script.');
    
    return {
      previewResults,
      errors,
      warnings,
      branchValidation,
      totalSessions: may27Sessions.length
    };
    
  } catch (error) {
    logger.error('💥 Fatal error during preview:', error);
    throw error;
  }
}

// Execute the preview
if (require.main === module) {
  previewClosingBalancesUpdate()
    .then((results) => {
      logger.info('✅ Preview execution completed');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('💥 Preview execution failed:', error);
      process.exit(1);
    });
}

module.exports = { previewClosingBalancesUpdate };
