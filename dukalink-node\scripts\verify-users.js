/**
 * Verify Users Script
 * Checks the current state of users in the database
 */

const { User, Role } = require("../src/models");
const logger = require("../src/utils/logger");
const sequelize = require("../config/database");

/**
 * List of emails to check (both old and new)
 */
const emailsToCheck = [
  "michael.kajib<PERSON>@simbatelecom.com",
  "micheal.kajib<PERSON>@simbatelecom.com",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
];

/**
 * Verify users in the database
 */
const verifyUsers = async () => {
  try {
    logger.info("🔍 Verifying users in database...");
    logger.info(`📧 Checking ${emailsToCheck.length} email addresses`);

    const results = {
      found: [],
      notFound: [],
      total: emailsToCheck.length,
    };

    // Check each email
    for (const email of emailsToCheck) {
      const user = await User.findOne({
        where: { email },
        include: [
          {
            model: Role,
            attributes: ["id", "name"],
          },
        ],
        attributes: [
          "id",
          "email",
          "name",
          "role_id",
          "created_at",
          "updated_at",
        ],
      });

      if (user) {
        results.found.push({
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.Role ? user.Role.name : "No role",
          created_at: user.created_at,
          updated_at: user.updated_at,
        });
        logger.info(
          `✅ Found: ${email} - ${user.name} (${user.Role ? user.Role.name : "No role"})`
        );
      } else {
        results.notFound.push(email);
        logger.info(`❌ Not found: ${email}`);
      }
    }

    // Display summary
    logger.info("\n📊 VERIFICATION SUMMARY:");
    logger.info(`✅ Users found: ${results.found.length}`);
    logger.info(`❌ Users not found: ${results.notFound.length}`);
    logger.info(`📧 Total emails checked: ${results.total}`);

    // Display found users in table format
    if (results.found.length > 0) {
      logger.info("\n👥 FOUND USERS:");
      console.table(
        results.found.map((user) => ({
          ID: user.id,
          Email: user.email,
          Name: user.name,
          Role: user.role,
          "Last Updated": user.updated_at.toISOString().split("T")[0],
        }))
      );
    }

    // Display not found emails
    if (results.notFound.length > 0) {
      logger.info("\n❌ NOT FOUND EMAILS:");
      results.notFound.forEach((email, index) => {
        logger.info(`${index + 1}. ${email}`);
      });
    }

    return results;
  } catch (error) {
    logger.error(`💥 Error during verification: ${error.message}`);
    throw error;
  }
};

/**
 * Get all users with their roles
 */
const getAllUsers = async () => {
  try {
    logger.info("\n👥 ALL USERS IN DATABASE:");

    const users = await User.findAll({
      include: [
        {
          model: Role,

          attributes: ["id", "name"],
        },
      ],
      attributes: [
        "id",
        "email",
        "name",
        "role_id",
        "created_at",
        "updated_at",
      ],
      order: [["email", "ASC"]],
    });

    logger.info(`📊 Total users in database: ${users.length}`);

    if (users.length > 0) {
      console.table(
        users.map((user) => ({
          ID: user.id,
          Email: user.email,
          Name: user.name,
          Role: user.Role ? user.Role.name : "No role",
          Created: user.created_at.toISOString().split("T")[0],
          Updated: user.updated_at.toISOString().split("T")[0],
        }))
      );
    }

    return users;
  } catch (error) {
    logger.error(`💥 Error getting all users: ${error.message}`);
    throw error;
  }
};

/**
 * Run the verification script
 */
const runVerification = async () => {
  try {
    // Test database connection
    await sequelize.authenticate();
    logger.info("✅ Database connection established");

    // Verify specific users
    await verifyUsers();

    // Get all users
    await getAllUsers();

    // Close database connection
    await sequelize.close();
    logger.info("✅ Database connection closed");

    process.exit(0);
  } catch (error) {
    logger.error(`💥 Verification failed: ${error.message}`);
    process.exit(1);
  }
};

// Run the script if called directly
if (require.main === module) {
  runVerification();
}

module.exports = {
  verifyUsers,
  getAllUsers,
  emailsToCheck,
};
