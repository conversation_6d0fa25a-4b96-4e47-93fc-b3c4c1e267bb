const path = require("path");
const {
  Branch,
  PosSession,
  PosSessionReconciliation,
} = require("../../src/models");
const sequelize = require("../../config/database");
const logger = require("../../src/utils/logger");

/**
 * FIX 28TH MAY OPENING BALANCES
 *
 * This script fixes the opening balances for 28th May sessions to match
 * the closing balances from 27th May. This is critical because when we
 * updated the 27th May closing balances, those should automatically
 * become the opening balances for 28th May sessions.
 *
 * MODES:
 * - PREVIEW: Shows what would be updated (NO DATABASE CHANGES)
 * - EXECUTE: Actually updates the opening balances (MODIFIES DATABASE)
 */

// Branches that need opening balance fixes based on our analysis
const openingBalanceFixes = [
  {
    branchName: "Kathonzweni",
    sessionId: 209,
    currentCash: 690104,
    currentFloat: 453685,
    correctCash: 29304,
    correctFloat: 935,
  },
  {
    branchName: "<PERSON><PERSON><PERSON><PERSON>",
    sessionId: 201,
    currentCash: 33748,
    currentFloat: -148941,
    correctCash: 30923,
    correctFloat: 11159,
  },
  {
    branchName: "Malaba",
    sessionId: 205,
    currentCash: 174598,
    currentFloat: 11018,
    correctCash: 21463,
    correctFloat: 24168,
  },
  {
    branchName: "Mariakani 2",
    sessionId: 200,
    currentCash: 4748,
    currentFloat: 19750,
    correctCash: 19750,
    correctFloat: 4748,
  },
  {
    branchName: "Mombasa ",
    sessionId: 166,
    currentCash: 0,
    currentFloat: 0,
    correctCash: 34821,
    correctFloat: 96381,
  },
  {
    branchName: "Prudential",
    sessionId: 185,
    currentCash: 54726,
    currentFloat: 10102,
    correctCash: 136521,
    correctFloat: 17602,
  },
  {
    branchName: "Voi",
    sessionId: 192,
    currentCash: 44339,
    currentFloat: -138266,
    correctCash: 21765,
    correctFloat: 38879,
  },
];

async function fixOpeningBalances28th(mode = "PREVIEW") {
  const isPreview = mode === "PREVIEW";
  const transaction = isPreview ? null : await sequelize.transaction();

  try {
    logger.info(`🔧 FIX 28TH MAY OPENING BALANCES - ${mode} MODE`);
    logger.info("=================================================");

    if (isPreview) {
      logger.info("👁️ PREVIEW MODE - NO DATABASE CHANGES WILL BE MADE");
      logger.info("📋 This will show what opening balances would be fixed");
    } else {
      logger.info("🚨 EXECUTE MODE - ACTUAL DATABASE CHANGES WILL BE MADE");
      logger.info(
        "⚠️ WARNING: This will modify 28th May session opening balances!"
      );
    }

    logger.info(
      "🎯 Target: Fix opening balances that don't match 27th May closing balances"
    );
    logger.info(`📊 Sessions to fix: ${openingBalanceFixes.length}`);

    const results = {
      sessionsAnalyzed: 0,
      sessionsUpdated: 0,
      sessionsAlreadyCorrect: 0,
      sessionsNotFound: 0,
      totalCashChanges: 0,
      totalFloatChanges: 0,
      errors: [],
      updates: [],
    };

    // Step 1: Process each session that needs fixing
    logger.info(
      "\n📋 Step 1: Processing sessions with incorrect opening balances..."
    );

    for (const fix of openingBalanceFixes) {
      logger.info(
        `\n🏪 Processing: ${fix.branchName} (Session ${fix.sessionId})`
      );

      results.sessionsAnalyzed++;

      // Get the session
      const session = await PosSession.findByPk(fix.sessionId, {
        include: [{ model: Branch }],
        ...(transaction && { transaction }),
      });

      if (!session) {
        const error = `Session ${fix.sessionId} not found`;
        results.errors.push(error);
        logger.error(`   ❌ ${error}`);
        results.sessionsNotFound++;
        continue;
      }

      logger.info(`   📋 Branch: ${session.Branch.name}`);
      logger.info(`   📅 Session: ${session.start_time}`);
      logger.info(
        `   📊 Current opening: Cash ${parseFloat(session.opening_cash_balance).toLocaleString()}, Float ${parseFloat(session.opening_mpesa_float).toLocaleString()}`
      );
      logger.info(
        `   ✅ Should be: Cash ${fix.correctCash.toLocaleString()}, Float ${fix.correctFloat.toLocaleString()}`
      );

      // Check if already correct
      const currentCash = parseFloat(session.opening_cash_balance);
      const currentFloat = parseFloat(session.opening_mpesa_float);

      const cashCorrect = Math.abs(currentCash - fix.correctCash) < 0.01;
      const floatCorrect = Math.abs(currentFloat - fix.correctFloat) < 0.01;

      if (cashCorrect && floatCorrect) {
        logger.info(`   ✅ Already correct - skipping`);
        results.sessionsAlreadyCorrect++;
        continue;
      }

      // Calculate changes
      const cashChange = fix.correctCash - currentCash;
      const floatChange = fix.correctFloat - currentFloat;

      logger.warn(`   🔄 NEEDS UPDATE:`);
      logger.warn(
        `      💰 Cash: ${currentCash.toLocaleString()} → ${fix.correctCash.toLocaleString()} (${cashChange >= 0 ? "+" : ""}${cashChange.toLocaleString()})`
      );
      logger.warn(
        `      📱 Float: ${currentFloat.toLocaleString()} → ${fix.correctFloat.toLocaleString()} (${floatChange >= 0 ? "+" : ""}${floatChange.toLocaleString()})`
      );

      // Record the update
      results.updates.push({
        branchName: fix.branchName,
        sessionId: fix.sessionId,
        cashChange,
        floatChange,
        oldCash: currentCash,
        oldFloat: currentFloat,
        newCash: fix.correctCash,
        newFloat: fix.correctFloat,
      });

      results.totalCashChanges += Math.abs(cashChange);
      results.totalFloatChanges += Math.abs(floatChange);

      // Execute update if not in preview mode
      if (!isPreview) {
        logger.info(`   🔧 Updating session ${fix.sessionId}...`);

        await session.update(
          {
            opening_cash_balance: fix.correctCash,
            opening_mpesa_float: fix.correctFloat,
          },
          { transaction }
        );

        logger.info(`   ✅ Successfully updated session ${fix.sessionId}`);
        results.sessionsUpdated++;
      } else {
        logger.info(`   👁️ PREVIEW: Would update session ${fix.sessionId}`);
      }
    }

    // Step 2: Summary and validation
    logger.info("\n📊 OPENING BALANCE FIX SUMMARY");
    logger.info("===============================");
    logger.info(`📋 Sessions analyzed: ${results.sessionsAnalyzed}`);
    logger.info(
      `✅ Sessions already correct: ${results.sessionsAlreadyCorrect}`
    );
    logger.info(
      `🔧 Sessions ${isPreview ? "to update" : "updated"}: ${isPreview ? results.updates.length : results.sessionsUpdated}`
    );
    logger.info(`❌ Sessions not found: ${results.sessionsNotFound}`);
    logger.info(`⚠️ Errors encountered: ${results.errors.length}`);

    // Detailed breakdown
    if (results.updates.length > 0) {
      logger.info(
        `\n${isPreview ? "👁️ SESSIONS TO UPDATE (PREVIEW):" : "🔧 SESSIONS UPDATED:"}`
      );
      results.updates.forEach((update) => {
        logger.info(`   • ${update.branchName} (Session ${update.sessionId})`);
        logger.info(
          `     Cash: ${update.oldCash.toLocaleString()} → ${update.newCash.toLocaleString()} (${update.cashChange >= 0 ? "+" : ""}${update.cashChange.toLocaleString()})`
        );
        logger.info(
          `     Float: ${update.oldFloat.toLocaleString()} → ${update.newFloat.toLocaleString()} (${update.floatChange >= 0 ? "+" : ""}${update.floatChange.toLocaleString()})`
        );
      });
    }

    if (results.errors.length > 0) {
      logger.info("\n❌ ERRORS ENCOUNTERED:");
      results.errors.forEach((error) => {
        logger.error(`   • ${error}`);
      });
    }

    // Summary statistics
    logger.info("\n📈 CHANGE STATISTICS:");
    logger.info(
      `💰 Total cash changes: ${results.totalCashChanges.toLocaleString()}`
    );
    logger.info(
      `📱 Total float changes: ${results.totalFloatChanges.toLocaleString()}`
    );

    // Commit or rollback
    if (!isPreview) {
      if (results.errors.length > 0) {
        await transaction.rollback();
        logger.error("\n💥 Errors encountered - transaction rolled back");
        throw new Error("Opening balance fix failed due to errors");
      } else {
        await transaction.commit();
        logger.info("\n✅ All changes committed successfully");
      }
    }

    // Final recommendations
    logger.info("\n💡 RECOMMENDATIONS:");
    if (isPreview) {
      if (results.updates.length > 0) {
        logger.info("   🔧 Run in EXECUTE mode to fix the opening balances");
        logger.info("   📋 Review the sessions to be updated above");
        logger.info("   ⚠️ Ensure you have database backups before executing");
      } else {
        logger.info(
          "   ✅ No opening balance fixes needed - all data is correct"
        );
      }
    } else {
      logger.info("   ✅ Opening balance fixes completed successfully");
      logger.info("   📋 28th May sessions now have correct opening balances");
      logger.info(
        "   🔍 Run verification check to confirm all balances are correct"
      );
    }

    const totalChanges = results.updates.length;
    logger.info(
      `\n📊 TOTAL ${isPreview ? "PLANNED" : "COMPLETED"} CHANGES: ${totalChanges}`
    );

    if (!isPreview && totalChanges > 0) {
      logger.info("\n🎉 Opening balance fixes completed successfully!");
      logger.info("💡 28th May sessions now have correct opening balances.");
      logger.info(
        "🔗 Opening balances now properly match 27th May closing balances."
      );
    }

    return results;
  } catch (error) {
    if (!isPreview && transaction) {
      await transaction.rollback();
    }
    logger.error("💥 Error during opening balance fix:", error);
    throw error;
  }
}

// Execute the fix
if (require.main === module) {
  const mode = process.argv[2] || "PREVIEW";

  if (!["PREVIEW", "EXECUTE"].includes(mode)) {
    console.error("Usage: node fix-opening-balances-28th.js [PREVIEW|EXECUTE]");
    process.exit(1);
  }

  fixOpeningBalances28th(mode)
    .then((results) => {
      logger.info(`✅ Opening balance fix ${mode.toLowerCase()} completed`);
      process.exit(0);
    })
    .catch((error) => {
      logger.error(
        `💥 Opening balance fix ${mode.toLowerCase()} failed:`,
        error
      );
      process.exit(1);
    });
}

module.exports = { fixOpeningBalances28th };
