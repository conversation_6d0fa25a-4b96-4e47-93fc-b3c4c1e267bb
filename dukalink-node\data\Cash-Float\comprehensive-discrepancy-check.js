const path = require("path");
const {
  Branch,
  PosSession,
  PosSessionReconciliation,
} = require("../../src/models");
const sequelize = require("../../config/database");
const logger = require("../../src/utils/logger");

/**
 * COMPREHENSIVE DISCREPANCY CHECK - NO DATABASE CHANGES
 * 
 * This script checks ALL branches for various discrepancies:
 * 1. Multiple reconciliations per session (like Mombasa had)
 * 2. Reconciliations with conflicting values
 * 3. Sessions without reconciliations
 * 4. Branches with unusual patterns
 * 5. Data integrity issues
 * 
 * ⚠️ READ-ONLY MODE - NO DATABASE CHANGES WILL BE MADE
 */

// Expected Excel data for validation
const expectedBalances = {
  "Bungoma": { cash: 13449, float: 10257 },
  "Busia": { cash: 17363, float: 14519 },
  "Chuka": { cash: 21801, float: 1928 },
  "Emali": { cash: 16150, float: 55958 },
  "Homabay": { cash: 70312, float: 4445 },
  "Kapenguria": { cash: 13325, float: 4161 },
  "Kapsabet": { cash: 5270, float: 13395 },
  "Karatina": { cash: 5425, float: 2073 },
  "Kathonzweni": { cash: 29304, float: 935 },
  "Kehancha": { cash: 750, float: 20832 },
  "Kericho": { cash: 20040, float: 3709 },
  "Kerugoya": { cash: 23390, float: 3792 },
  "Kibwezi": { cash: 16430, float: 17145 },
  "Kikima": { cash: 7985, float: 80133 },
  "Kilgoris": { cash: 30923, float: 11159 },
  "Kilifi": { cash: 7275, float: 26675 },
  "Kisumu": { cash: 72153, float: 43634 },
  "Kitale 2": { cash: 24014, float: 14414 },
  "Kitui": { cash: 21014, float: 2016 },
  "Likoni": { cash: 34535, float: 17243 },
  "Machakos": { cash: 28009, float: 13725 },
  "Magunga": { cash: 9805, float: 26438 },
  "Malaba": { cash: 21463, float: 24168 },
  "Mariakani 2": { cash: 19750, float: 4748 },
  "Mau summit": { cash: 34440, float: 9560 },
  "Maua": { cash: 14920, float: 16424 },
  "Mbale": { cash: 19145, float: 68177 },
  "Mbita": { cash: 28038, float: 624 },
  "Mombasa ": { cash: 34821, float: 96381 },
  "Mtwapa": { cash: 5696, float: 54296 },
  "Muhuru Bay": { cash: 18140, float: 15260 },
  "Mumias": { cash: 3572, float: 11060 },
  "Mutomo": { cash: 10365, float: 29463 },
  "Mwatate": { cash: 12668, float: 9057 },
  "Mwingi": { cash: 4304, float: 15261 },
  "Nkubu": { cash: 18900, float: 656 },
  "Nyamira": { cash: 40006, float: 1896 },
  "Nyeri": { cash: 28769, float: 20628 },
  "Oyugis": { cash: 12518, float: 13794 },
  "Prudential": { cash: 136521, float: 17602 },
  "Sori": { cash: 32, float: 2599 },
  "Voi": { cash: 21765, float: 38879 },
  "Webuye": { cash: 39962, float: 17681 },
  "Wote": { cash: 16041, float: 5890 },
  "Wundanyi": { cash: 9830, float: 12458 }
};

async function comprehensiveDiscrepancyCheck() {
  try {
    logger.info('🔍 COMPREHENSIVE DISCREPANCY CHECK - READ-ONLY MODE');
    logger.info('====================================================');
    logger.info('📅 Checking ALL branches for data integrity issues...');
    logger.info('⚠️ NO DATABASE CHANGES WILL BE MADE');
    
    const issues = {
      multipleReconciliations: [],
      conflictingValues: [],
      missingReconciliations: [],
      unexpectedBranches: [],
      extremeValues: [],
      negativeValues: [],
      duplicateReconciliations: [],
      inconsistentSessions: [],
      dataIntegrityIssues: []
    };
    
    // Step 1: Get all branches with 27th May sessions
    logger.info('\n📋 Step 1: Loading all branches with 27th May sessions...');
    
    const targetDate = '2025-05-27';
    const startOfDay = `${targetDate} 00:00:00`;
    const endOfDay = `${targetDate} 23:59:59`;
    
    const { Op } = require('sequelize');
    
    const allBranches = await Branch.findAll({
      where: { deleted_at: null },
      include: [
        {
          model: PosSession,
          where: {
            start_time: {
              [Op.between]: [startOfDay, endOfDay]
            },
            deleted_at: null
          },
          required: false
        }
      ]
    });
    
    const branchesWithSessions = allBranches.filter(b => b.PosSessions && b.PosSessions.length > 0);
    logger.info(`📊 Found ${branchesWithSessions.length} branches with 27th May sessions`);
    
    // Step 2: Detailed analysis of each branch
    logger.info('\n📋 Step 2: Analyzing each branch for discrepancies...');
    
    for (const branch of branchesWithSessions) {
      logger.info(`\n🏪 Analyzing: ${branch.name} (ID: ${branch.id})`);
      logger.info(`   📋 Sessions: ${branch.PosSessions.length}`);
      
      const expectedData = expectedBalances[branch.name];
      const isInExcel = !!expectedData;
      
      if (!isInExcel) {
        issues.unexpectedBranches.push({
          branchName: branch.name,
          branchId: branch.id,
          sessionCount: branch.PosSessions.length,
          reason: 'Branch has 27th May sessions but not in Excel'
        });
        logger.warn(`   ⚠️ Branch not in Excel but has ${branch.PosSessions.length} session(s)`);
      }
      
      // Analyze each session
      for (const session of branch.PosSessions) {
        logger.info(`\n   🔍 Session ${session.id} (${session.start_time})`);
        
        // Get all reconciliations for this session
        const reconciliations = await PosSessionReconciliation.findAll({
          where: { pos_session_id: session.id },
          order: [['created_at', 'ASC']]
        });
        
        logger.info(`      📝 Reconciliations: ${reconciliations.length}`);
        
        if (reconciliations.length === 0) {
          issues.missingReconciliations.push({
            branchName: branch.name,
            sessionId: session.id,
            reason: 'Session has no reconciliation'
          });
          logger.error(`      ❌ No reconciliations found`);
          continue;
        }
        
        if (reconciliations.length > 1) {
          issues.multipleReconciliations.push({
            branchName: branch.name,
            sessionId: session.id,
            reconciliationCount: reconciliations.length,
            reconciliations: reconciliations.map(r => ({
              id: r.id,
              cash: parseFloat(r.closing_cash_balance || 0),
              float: parseFloat(r.closing_mpesa_float || 0),
              notes: r.notes,
              created_at: r.created_at
            }))
          });
          logger.warn(`      ⚠️ Multiple reconciliations found: ${reconciliations.length}`);
          
          // Check for conflicting values
          const uniqueValues = new Set();
          reconciliations.forEach(rec => {
            const cash = parseFloat(rec.closing_cash_balance || 0);
            const float = parseFloat(rec.closing_mpesa_float || 0);
            uniqueValues.add(`${cash}-${float}`);
          });
          
          if (uniqueValues.size > 1) {
            issues.conflictingValues.push({
              branchName: branch.name,
              sessionId: session.id,
              reconciliations: reconciliations.map(r => ({
                id: r.id,
                cash: parseFloat(r.closing_cash_balance || 0),
                float: parseFloat(r.closing_mpesa_float || 0),
                notes: r.notes
              }))
            });
            logger.error(`      ❌ Conflicting values between reconciliations`);
          }
        }
        
        // Analyze each reconciliation
        reconciliations.forEach((rec, index) => {
          const cash = parseFloat(rec.closing_cash_balance || 0);
          const float = parseFloat(rec.closing_mpesa_float || 0);
          
          logger.info(`         ${index + 1}. ID: ${rec.id}, Cash: ${cash.toLocaleString()}, Float: ${float.toLocaleString()}`);
          logger.info(`            Notes: ${rec.notes || 'No notes'}`);
          logger.info(`            Created: ${rec.created_at}`);
          
          // Check for extreme values
          if (Math.abs(cash) > 1000000 || Math.abs(float) > 1000000) {
            issues.extremeValues.push({
              branchName: branch.name,
              sessionId: session.id,
              reconciliationId: rec.id,
              cash,
              float,
              reason: 'Values exceed 1 million'
            });
            logger.warn(`            ⚠️ Extreme values detected`);
          }
          
          // Check for negative values
          if (cash < 0 || float < 0) {
            issues.negativeValues.push({
              branchName: branch.name,
              sessionId: session.id,
              reconciliationId: rec.id,
              cash,
              float,
              reason: 'Negative values detected'
            });
            logger.warn(`            ⚠️ Negative values detected`);
          }
          
          // Compare with expected values if available
          if (isInExcel && expectedData) {
            const cashMatches = Math.abs(cash - expectedData.cash) < 0.01;
            const floatMatches = Math.abs(float - expectedData.float) < 0.01;
            
            if (cashMatches && floatMatches) {
              logger.info(`            ✅ Matches Excel values`);
            } else {
              logger.warn(`            ⚠️ Differs from Excel: Expected Cash ${expectedData.cash.toLocaleString()}, Float ${expectedData.float.toLocaleString()}`);
            }
          }
        });
      }
    }
    
    // Step 3: Summary Report
    logger.info('\n📊 COMPREHENSIVE DISCREPANCY ANALYSIS');
    logger.info('=====================================');
    
    const totalIssues = Object.values(issues).reduce((sum, arr) => sum + arr.length, 0);
    logger.info(`📋 Total branches analyzed: ${branchesWithSessions.length}`);
    logger.info(`⚠️ Total issues found: ${totalIssues}`);
    
    // Multiple Reconciliations
    if (issues.multipleReconciliations.length > 0) {
      logger.info(`\n🔄 MULTIPLE RECONCILIATIONS (${issues.multipleReconciliations.length}):`);
      issues.multipleReconciliations.forEach(issue => {
        logger.info(`   • ${issue.branchName} (Session ${issue.sessionId}): ${issue.reconciliationCount} reconciliations`);
        issue.reconciliations.forEach((rec, idx) => {
          logger.info(`     ${idx + 1}. ID ${rec.id}: Cash ${rec.cash.toLocaleString()}, Float ${rec.float.toLocaleString()}`);
        });
      });
    }
    
    // Conflicting Values
    if (issues.conflictingValues.length > 0) {
      logger.info(`\n❌ CONFLICTING VALUES (${issues.conflictingValues.length}):`);
      issues.conflictingValues.forEach(issue => {
        logger.info(`   • ${issue.branchName} (Session ${issue.sessionId}):`);
        issue.reconciliations.forEach((rec, idx) => {
          logger.info(`     ${idx + 1}. ID ${rec.id}: Cash ${rec.cash.toLocaleString()}, Float ${rec.float.toLocaleString()}`);
        });
      });
    }
    
    // Missing Reconciliations
    if (issues.missingReconciliations.length > 0) {
      logger.info(`\n❌ MISSING RECONCILIATIONS (${issues.missingReconciliations.length}):`);
      issues.missingReconciliations.forEach(issue => {
        logger.info(`   • ${issue.branchName} (Session ${issue.sessionId})`);
      });
    }
    
    // Unexpected Branches
    if (issues.unexpectedBranches.length > 0) {
      logger.info(`\n⚠️ UNEXPECTED BRANCHES (${issues.unexpectedBranches.length}):`);
      issues.unexpectedBranches.forEach(issue => {
        logger.info(`   • ${issue.branchName} (${issue.sessionCount} session(s)) - Not in Excel`);
      });
    }
    
    // Extreme Values
    if (issues.extremeValues.length > 0) {
      logger.info(`\n⚠️ EXTREME VALUES (${issues.extremeValues.length}):`);
      issues.extremeValues.forEach(issue => {
        logger.info(`   • ${issue.branchName} (Session ${issue.sessionId}, Rec ${issue.reconciliationId}): Cash ${issue.cash.toLocaleString()}, Float ${issue.float.toLocaleString()}`);
      });
    }
    
    // Negative Values
    if (issues.negativeValues.length > 0) {
      logger.info(`\n⚠️ NEGATIVE VALUES (${issues.negativeValues.length}):`);
      issues.negativeValues.forEach(issue => {
        logger.info(`   • ${issue.branchName} (Session ${issue.sessionId}, Rec ${issue.reconciliationId}): Cash ${issue.cash.toLocaleString()}, Float ${issue.float.toLocaleString()}`);
      });
    }
    
    // Recommendations
    logger.info('\n💡 RECOMMENDATIONS:');
    if (issues.multipleReconciliations.length > 0) {
      logger.info('   🔧 Review multiple reconciliations - some may need cleanup like Mombasa');
    }
    if (issues.conflictingValues.length > 0) {
      logger.info('   🔧 Resolve conflicting values - determine which reconciliation is correct');
    }
    if (issues.extremeValues.length > 0) {
      logger.info('   🔧 Investigate extreme values - may indicate data entry errors');
    }
    if (issues.negativeValues.length > 0) {
      logger.info('   🔧 Review negative values - may indicate system issues');
    }
    if (issues.unexpectedBranches.length > 0) {
      logger.info('   🔧 Check unexpected branches - may need to be added to Excel or excluded');
    }
    
    if (totalIssues === 0) {
      logger.info('   ✅ No significant issues found - data appears clean!');
    }
    
    logger.info('\n🎉 Comprehensive discrepancy check completed!');
    logger.info('💡 Review the findings above before proceeding with any updates.');
    
    return issues;
    
  } catch (error) {
    logger.error('💥 Error during comprehensive discrepancy check:', error);
    throw error;
  }
}

// Execute the check
if (require.main === module) {
  comprehensiveDiscrepancyCheck()
    .then((issues) => {
      logger.info('✅ Comprehensive discrepancy check completed');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('💥 Comprehensive discrepancy check failed:', error);
      process.exit(1);
    });
}

module.exports = { comprehensiveDiscrepancyCheck };
