import * as XLSX from "xlsx";
import { format } from "date-fns";

/**
 * Interface for DSA assignment group data structure
 */
interface DsaAssignmentGroup {
  assignment_identifier: string;
  dsa_agent_name: string;
  branch_name: string;
  created_at: string;
  reconciled: boolean;
  reconciled_at?: string | null;
  items?: Array<{
    id: number;
    product_id: number;
    quantity_assigned: number;
    quantity_returned: number;
    quantity_sold?: number;
    Product?: {
      name: string;
      sku: string;
    };
  }>;
}

/**
 * Export DSA assignments to Excel
 * @param assignments List of DSA assignment groups to export
 * @param filename Filename for the Excel file (without extension)
 */
export function exportDsaAssignmentsToExcel(
  assignments: DsaAssignmentGroup[],
  filename: string = "dsa-assignments-export"
) {
  if (!assignments || assignments.length === 0) {
    throw new Error("No assignments to export");
  }

  // Define headers for summary sheet
  const summaryHeaders = [
    "Assignment ID",
    "DSA Agent",
    "Branch",
    "Date Created",
    "Status",
    "Reconciled Date",
    "Total Items",
    "Total Quantity Assigned",
    "Total Quantity Returned"
  ];

  // Define headers for detailed sheet
  const detailHeaders = [
    "Assignment ID",
    "DSA Agent",
    "Branch",
    "Product Name",
    "Product SKU",
    "Quantity Assigned",
    "Quantity Returned",
    "Quantity Sold",
    "Date Created",
    "Status"
  ];

  // Create summary data
  const summaryData = [summaryHeaders];
  
  // Create detailed data
  const detailData = [detailHeaders];

  assignments.forEach((assignment) => {
    const totalItems = assignment.items?.length || 0;
    const totalQuantityAssigned = assignment.items?.reduce(
      (sum, item) => sum + (item.quantity_assigned || 0), 0
    ) || 0;
    const totalQuantityReturned = assignment.items?.reduce(
      (sum, item) => sum + (item.quantity_returned || 0), 0
    ) || 0;

    // Add summary row
    const summaryRow = [
      assignment.assignment_identifier,
      assignment.dsa_agent_name || "N/A",
      assignment.branch_name || "N/A",
      assignment.created_at ? format(new Date(assignment.created_at), "yyyy-MM-dd HH:mm") : "N/A",
      assignment.reconciled ? "Reconciled" : "Unreconciled",
      assignment.reconciled_at ? format(new Date(assignment.reconciled_at), "yyyy-MM-dd HH:mm") : "N/A",
      totalItems,
      totalQuantityAssigned,
      totalQuantityReturned
    ];
    summaryData.push(summaryRow);

    // Add detailed rows for each item
    if (assignment.items && assignment.items.length > 0) {
      assignment.items.forEach((item) => {
        const detailRow = [
          assignment.assignment_identifier,
          assignment.dsa_agent_name || "N/A",
          assignment.branch_name || "N/A",
          item.Product?.name || "N/A",
          item.Product?.sku || "N/A",
          item.quantity_assigned || 0,
          item.quantity_returned || 0,
          item.quantity_sold || 0,
          assignment.created_at ? format(new Date(assignment.created_at), "yyyy-MM-dd HH:mm") : "N/A",
          assignment.reconciled ? "Reconciled" : "Unreconciled"
        ];
        detailData.push(detailRow);
      });
    } else {
      // If no items, add a row with N/A for product details
      const detailRow = [
        assignment.assignment_identifier,
        assignment.dsa_agent_name || "N/A",
        assignment.branch_name || "N/A",
        "N/A",
        "N/A",
        0,
        0,
        0,
        assignment.created_at ? format(new Date(assignment.created_at), "yyyy-MM-dd HH:mm") : "N/A",
        assignment.reconciled ? "Reconciled" : "Unreconciled"
      ];
      detailData.push(detailRow);
    }
  });

  // Set column widths for summary sheet
  const summaryColWidths = [
    { wch: 15 }, // Assignment ID
    { wch: 20 }, // DSA Agent
    { wch: 15 }, // Branch
    { wch: 18 }, // Date Created
    { wch: 12 }, // Status
    { wch: 18 }, // Reconciled Date
    { wch: 12 }, // Total Items
    { wch: 18 }, // Total Quantity Assigned
    { wch: 18 }, // Total Quantity Returned
  ];

  // Set column widths for detail sheet
  const detailColWidths = [
    { wch: 15 }, // Assignment ID
    { wch: 20 }, // DSA Agent
    { wch: 15 }, // Branch
    { wch: 25 }, // Product Name
    { wch: 15 }, // Product SKU
    { wch: 15 }, // Quantity Assigned
    { wch: 15 }, // Quantity Returned
    { wch: 12 }, // Quantity Sold
    { wch: 18 }, // Date Created
    { wch: 12 }, // Status
  ];

  // Create summary worksheet
  const summaryWs = XLSX.utils.aoa_to_sheet(summaryData);
  summaryWs["!cols"] = summaryColWidths;

  // Create detail worksheet
  const detailWs = XLSX.utils.aoa_to_sheet(detailData);
  detailWs["!cols"] = detailColWidths;

  // Apply styles to header rows
  const headerStyle = {
    font: { bold: true },
    fill: { fgColor: { rgb: "EFEFEF" } },
    alignment: { horizontal: "center", vertical: "center" }
  };

  // Apply styles to summary sheet headers
  for (let i = 0; i < summaryHeaders.length; i++) {
    const cellRef = XLSX.utils.encode_cell({ r: 0, c: i });
    if (!summaryWs[cellRef]) summaryWs[cellRef] = {};
    summaryWs[cellRef].s = headerStyle;
  }

  // Apply styles to detail sheet headers
  for (let i = 0; i < detailHeaders.length; i++) {
    const cellRef = XLSX.utils.encode_cell({ r: 0, c: i });
    if (!detailWs[cellRef]) detailWs[cellRef] = {};
    detailWs[cellRef].s = headerStyle;
  }

  // Create workbook
  const wb = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(wb, summaryWs, "Summary");
  XLSX.utils.book_append_sheet(wb, detailWs, "Details");

  // Generate Excel file and trigger download
  XLSX.writeFile(wb, `${filename}.xlsx`);
}
