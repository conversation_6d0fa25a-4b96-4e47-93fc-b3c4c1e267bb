#!/usr/bin/env node

/**
 * IMPROVED PREVIEW RUNNER - NO DATABASE CHANGES
 * 
 * This script runs the improved preview that fixes issues found in the original script:
 * 1. Includes Voi branch (was wrongly excluded)
 * 2. Corrects data discrepancies found between Excel and hardcoded data
 * 3. Provides comprehensive analysis of all branches
 * 
 * Run with: node data/cash-float/run-improved-preview.js
 */

const path = require('path');

// Set up environment
require('dotenv').config({ path: path.join(__dirname, '../../.env') });

// Import and run the improved preview function
const { improvedPreviewClosingBalances } = require('./improved-preview-closing-balances');

console.log('🔍 IMPROVED PREVIEW MODE - Starting comprehensive analysis...');
console.log('==============================================================');
console.log('⚠️  NO DATABASE CHANGES WILL BE MADE');
console.log('🔧 IMPROVEMENTS IN THIS VERSION:');
console.log('   ✅ Includes Voi branch (was wrongly excluded)');
console.log('   ✅ Corrects Mariakani 2 values (19,750 cash, 4,748 float)');
console.log('   ✅ Corrects Prudential values (136,521 cash, 17,602 float)');
console.log('   ✅ Fixes branch name mapping issues');
console.log('   ✅ Comprehensive coverage of all Excel branches');
console.log('==============================================================\n');

improvedPreviewClosingBalances()
  .then((results) => {
    console.log('\n✅ Improved preview completed successfully!');
    console.log('📊 SUMMARY:');
    console.log(`   • Excel branches: ${results.excelBranches}`);
    console.log(`   • Found in DB: ${results.foundInDB}`);
    console.log(`   • Sessions to create: ${results.sessionsToCreate.length}`);
    console.log(`   • Reconciliations to create: ${results.reconciliationsToCreate.length}`);
    console.log(`   • Reconciliations to update: ${results.reconciliationsToUpdate.length}`);
    console.log(`   • Already correct: ${results.correctValues}`);
    console.log(`   • Missing branches: ${results.missingBranches.length}`);
    
    const totalChanges = results.sessionsToCreate.length + results.reconciliationsToCreate.length + results.reconciliationsToUpdate.length;
    console.log(`   • Total changes needed: ${totalChanges}`);
    
    if (results.missingBranches.length > 0) {
      console.log('\n❌ BRANCHES NOT FOUND IN DATABASE:');
      results.missingBranches.forEach(branch => {
        console.log(`   • ${branch}`);
      });
    }
    
    console.log('\n🔧 KEY FIXES APPLIED:');
    console.log('   ✅ Voi branch now included (Cash: 21,765, Float: 38,879)');
    console.log('   ✅ Mariakani 2 values corrected');
    console.log('   ✅ Prudential values corrected');
    console.log('   ✅ Branch name mapping improved');
    
    console.log('\n💡 Review the detailed logs above before proceeding with actual updates.');
    console.log('🎯 This improved version provides comprehensive coverage of all Excel data.');
    
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 Improved preview failed:', error.message);
    console.error(error.stack);
    process.exit(1);
  });
