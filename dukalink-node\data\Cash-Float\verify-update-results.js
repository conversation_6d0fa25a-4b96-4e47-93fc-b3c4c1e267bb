const path = require("path");
const {
  Branch,
  PosSession,
  PosSessionReconciliation,
} = require("../../src/models");
const sequelize = require("../../config/database");
const logger = require("../../src/utils/logger");

/**
 * VERIFICATION SCRIPT - READ-ONLY
 * 
 * This script verifies the results of the closing balances update by:
 * 1. Checking all branches have sessions on 27th May
 * 2. Verifying reconciliation records exist and have correct values
 * 3. Comparing actual values with expected values from Excel
 * 4. Generating a verification report
 */

// Expected values from Excel for verification
const expectedBalances = [
  { branch: "Bungoma", cash: 13449, float: 10257 },
  { branch: "Busia", cash: 17363, float: 14519 },
  { branch: "Chuka", cash: 21801, float: 1928 },
  { branch: "<PERSON><PERSON>", cash: 16150, float: 55958 },
  { branch: "Homabay", cash: 70312, float: 4445 },
  { branch: "Kapenguria", cash: 13325, float: 4161 },
  { branch: "Kapsabe<PERSON>", cash: 5270, float: 13395 },
  { branch: "Karatina", cash: 5425, float: 2073 },
  { branch: "Kathonzweni", cash: 29304, float: 935 },
  { branch: "Kehancha", cash: 750, float: 20832 },
  { branch: "Kericho", cash: 20040, float: 3709 },
  { branch: "Kerugoya", cash: 23390, float: 3792 },
  { branch: "Kibwezi", cash: 16430, float: 17145 },
  { branch: "Kikima", cash: 7985, float: 80133 },
  { branch: "Kilgoris", cash: 30923, float: 11159 },
  { branch: "Kilifi", cash: 7275, float: 26675 },
  { branch: "Kisumu", cash: 72153, float: 43634 },
  { branch: "Kitale 2", cash: 24014, float: 14414 },
  { branch: "Kitui", cash: 21014, float: 2016 },
  { branch: "Likoni", cash: 34535, float: 17243 },
  { branch: "Machakos", cash: 28009, float: 13725 },
  { branch: "Magunga", cash: 9805, float: 26438 },
  { branch: "Malaba", cash: 21463, float: 24168 },
  { branch: "Mariakani 2", cash: 4748, float: 19750 },
  { branch: "Mau summit", cash: 34440, float: 9560 },
  { branch: "Maua", cash: 14920, float: 16424 },
  { branch: "Mbale", cash: 19145, float: 68177 },
  { branch: "Mbita", cash: 28038, float: 624 },
  { branch: "Mombasa ", cash: 34821, float: 96381 },
  { branch: "Mtwapa", cash: 5696, float: 54296 },
  { branch: "Muhuru Bay", cash: 18140, float: 15260 },
  { branch: "Mumias", cash: 3572, float: 11060 },
  { branch: "Mutomo", cash: 10365, float: 29463 },
  { branch: "Mwatate", cash: 12668, float: 9057 },
  { branch: "Mwingi", cash: 4304, float: 15261 },
  { branch: "Nkubu", cash: 18900, float: 656 },
  { branch: "Nyamira", cash: 40006, float: 1896 },
  { branch: "Nyeri", cash: 28769, float: 20628 },
  { branch: "Oyugis", cash: 12518, float: 13794 },
  { branch: "Prudential", cash: 54726, float: 10102 },
  { branch: "Sori", cash: 32, float: 2599 },
  { branch: "Webuye", cash: 39962, float: 17681 },
  { branch: "Wote", cash: 16041, float: 5890 },
  { branch: "Wundanyi", cash: 9830, float: 12458 }
];

async function verifyUpdateResults() {
  try {
    logger.info('🔍 VERIFYING CLOSING BALANCES UPDATE RESULTS');
    logger.info('============================================');
    logger.info('📅 Checking 27th May 2025 sessions and reconciliations...');
    
    const verification = {
      branchesChecked: 0,
      sessionsFound: 0,
      reconciliationsFound: 0,
      correctValues: 0,
      incorrectValues: 0,
      missingData: 0,
      errors: []
    };
    
    // Get all sessions from 27th May with reconciliations
    const targetDate = '2025-05-27';
    const startOfDay = `${targetDate} 00:00:00`;
    const endOfDay = `${targetDate} 23:59:59`;
    
    const { Op } = require('sequelize');
    
    const may27Sessions = await PosSession.findAll({
      where: {
        start_time: {
          [Op.between]: [startOfDay, endOfDay]
        },
        deleted_at: null
      },
      include: [
        {
          model: Branch,
          attributes: ['id', 'name', 'location']
        },
        {
          model: PosSessionReconciliation,
          as: 'reconciliation',
          required: false
        }
      ],
      order: [['start_time', 'ASC']]
    });
    
    logger.info(`📊 Found ${may27Sessions.length} POS sessions from 27th May 2025`);
    
    // Verify each expected branch
    for (const expected of expectedBalances) {
      const { branch: branchName, cash: expectedCash, float: expectedFloat } = expected;
      
      verification.branchesChecked++;
      logger.info(`\n🏪 Verifying: ${branchName}`);
      logger.info(`   📋 Expected: Cash ${expectedCash.toLocaleString()}, Float ${expectedFloat.toLocaleString()}`);
      
      // Find sessions for this branch
      const branchSessions = may27Sessions.filter(session => 
        session.Branch && session.Branch.name === branchName
      );
      
      if (branchSessions.length === 0) {
        verification.missingData++;
        verification.errors.push(`No sessions found for ${branchName} on 27th May`);
        logger.error(`   ❌ No sessions found`);
        continue;
      }
      
      verification.sessionsFound += branchSessions.length;
      logger.info(`   📋 Found ${branchSessions.length} session(s)`);
      
      let hasCorrectValues = false;
      
      // Check each session's reconciliation
      for (const session of branchSessions) {
        logger.info(`\n   🔍 Session ID: ${session.id} (${session.start_time})`);
        
        const reconciliation = session.reconciliation;
        
        if (!reconciliation) {
          verification.missingData++;
          logger.warn(`      ⚠️ No reconciliation found`);
          continue;
        }
        
        verification.reconciliationsFound++;
        
        const actualCash = parseFloat(reconciliation.closing_cash_balance || 0);
        const actualFloat = parseFloat(reconciliation.closing_mpesa_float || 0);
        
        logger.info(`      📝 Reconciliation ID: ${reconciliation.id}`);
        logger.info(`      💰 Actual: Cash ${actualCash.toLocaleString()}, Float ${actualFloat.toLocaleString()}`);
        
        // Check if values match expected
        const cashMatches = Math.abs(actualCash - expectedCash) < 0.01; // Allow for small floating point differences
        const floatMatches = Math.abs(actualFloat - expectedFloat) < 0.01;
        
        if (cashMatches && floatMatches) {
          logger.info(`      ✅ Values match expected amounts`);
          hasCorrectValues = true;
        } else {
          logger.warn(`      ⚠️ Values don't match:`);
          if (!cashMatches) {
            logger.warn(`         💰 Cash: Expected ${expectedCash}, Got ${actualCash} (Diff: ${actualCash - expectedCash})`);
          }
          if (!floatMatches) {
            logger.warn(`         📱 Float: Expected ${expectedFloat}, Got ${actualFloat} (Diff: ${actualFloat - expectedFloat})`);
          }
        }
      }
      
      if (hasCorrectValues) {
        verification.correctValues++;
      } else {
        verification.incorrectValues++;
        verification.errors.push(`${branchName}: No sessions with correct values found`);
      }
    }
    
    // Generate verification report
    logger.info('\n📊 VERIFICATION REPORT');
    logger.info('======================');
    logger.info(`🏪 Branches checked: ${verification.branchesChecked}`);
    logger.info(`📅 Sessions found: ${verification.sessionsFound}`);
    logger.info(`📝 Reconciliations found: ${verification.reconciliationsFound}`);
    logger.info(`✅ Branches with correct values: ${verification.correctValues}`);
    logger.info(`⚠️ Branches with incorrect values: ${verification.incorrectValues}`);
    logger.info(`❌ Branches with missing data: ${verification.missingData}`);
    logger.info(`🚨 Total errors: ${verification.errors.length}`);
    
    // Calculate success rate
    const successRate = (verification.correctValues / verification.branchesChecked * 100).toFixed(1);
    logger.info(`📈 Success rate: ${successRate}%`);
    
    if (verification.errors.length > 0) {
      logger.info('\n🚨 ERRORS FOUND:');
      verification.errors.forEach((error, index) => {
        logger.error(`${index + 1}. ${error}`);
      });
    }
    
    // Overall assessment
    if (verification.correctValues === verification.branchesChecked) {
      logger.info('\n🎉 VERIFICATION PASSED!');
      logger.info('All branches have correct closing balances.');
    } else if (verification.correctValues > verification.branchesChecked * 0.9) {
      logger.info('\n⚠️ VERIFICATION MOSTLY PASSED');
      logger.info('Most branches have correct values, but some issues found.');
    } else {
      logger.info('\n❌ VERIFICATION FAILED');
      logger.info('Significant issues found with the update.');
    }
    
    return verification;
    
  } catch (error) {
    logger.error('💥 Verification failed:', error);
    throw error;
  }
}

// Execute verification
if (require.main === module) {
  verifyUpdateResults()
    .then((results) => {
      logger.info('✅ Verification completed');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('💥 Verification execution failed:', error);
      process.exit(1);
    });
}

module.exports = { verifyUpdateResults };
