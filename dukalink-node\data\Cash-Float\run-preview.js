#!/usr/bin/env node

/**
 * SAFE PREVIEW RUNNER - NO DATABASE CHANGES
 * 
 * This script runs a complete preview/simulation of the closing balances update
 * Shows exactly what would be changed without making any database modifications
 * 
 * Run with: node data/cash-float/run-preview.js
 */

const path = require('path');

// Set up environment
require('dotenv').config({ path: path.join(__dirname, '../../.env') });

// Import and run the preview function
const { previewClosingBalancesUpdate } = require('./preview-closing-balances');

console.log('🔍 PREVIEW MODE - Starting 27th balances analysis...');
console.log('====================================================');
console.log('⚠️  NO DATABASE CHANGES WILL BE MADE');
console.log('📊 This will show what WOULD be updated');
console.log('====================================================\n');

previewClosingBalancesUpdate()
  .then((results) => {
    console.log('\n✅ Preview completed successfully!');
    console.log(`📊 Summary: ${results.previewResults.length} changes identified`);
    console.log(`⚠️  Warnings: ${results.warnings.length}`);
    console.log(`❌ Errors: ${results.errors.length}`);
    console.log('\n💡 Review the detailed logs above before proceeding with actual updates.');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 Preview failed:', error.message);
    console.error(error.stack);
    process.exit(1);
  });
