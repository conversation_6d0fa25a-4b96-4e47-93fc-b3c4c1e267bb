const path = require("path");
const {
  Branch,
  PosSession,
  PosSessionReconciliation,
  User,
} = require("../../src/models");
const sequelize = require("../../config/database");
const logger = require("../../src/utils/logger");

/**
 * IMPROVED UPDATE SCRIPT - APPLIES ACTUAL DATABASE CHANGES
 * 
 * This script applies the corrected Excel data to the database
 * Fixes issues found in the previous script:
 * 1. Includes Voi branch (was wrongly excluded)
 * 2. Corrects data discrepancies found in Excel vs hardcoded data
 * 3. Provides comprehensive coverage of all branches
 * 
 * ⚠️ WARNING: THIS SCRIPT MAKES ACTUAL DATABASE CHANGES
 */

// Corrected Excel data from 27th balances.xlsx (read directly from Excel)
const balancesDataFromExcel = [
  { branch: "Bungoma", cash: 13449, float: 10257 },
  { branch: "<PERSON><PERSON>", cash: 17363, float: 14519 },
  { branch: "<PERSON><PERSON>", cash: 21801, float: 1928 },
  { branch: "<PERSON><PERSON>", cash: 16150, float: 55958 },
  { branch: "Homa<PERSON>", cash: 70312, float: 4445 },
  { branch: "Kapenguria", cash: 13325, float: 4161 },
  { branch: "Kapsabet", cash: 5270, float: 13395 },
  { branch: "Karatina", cash: 5425, float: 2073 },
  { branch: "Kathonzweni", cash: 29304, float: 935 },
  { branch: "Kehancha", cash: 750, float: 20832 },
  { branch: "Kericho", cash: 20040, float: 3709 },
  { branch: "Kerugoya", cash: 23390, float: 3792 },
  { branch: "Kibwezi", cash: 16430, float: 17145 },
  { branch: "Kikima", cash: 7985, float: 80133 },
  { branch: "Kilgoris", cash: 30923, float: 11159 },
  { branch: "Kilifi", cash: 7275, float: 26675 },
  { branch: "Kisumu", cash: 72153, float: 43634 },
  { branch: "Kitale 2", cash: 24014, float: 14414 },
  { branch: "Kitui", cash: 21014, float: 2016 },
  { branch: "Likoni", cash: 34535, float: 17243 },
  { branch: "Machakos", cash: 28009, float: 13725 },
  { branch: "Magunga", cash: 9805, float: 26438 },
  { branch: "Malaba", cash: 21463, float: 24168 },
  { branch: "Mariakani 2", cash: 19750, float: 4748 }, // CORRECTED: was swapped in previous script
  { branch: "Mau Summit", cash: 34440, float: 9560 }, // CORRECTED: case sensitivity
  { branch: "Maua", cash: 14920, float: 16424 },
  { branch: "Mbale", cash: 19145, float: 68177 },
  { branch: "Mbita", cash: 28038, float: 624 },
  { branch: "Mombasa", cash: 34821, float: 96381 }, // CORRECTED: removed trailing space
  { branch: "Mtwapa", cash: 5696, float: 54296 },
  { branch: "Muhuru Bay", cash: 18140, float: 15260 },
  { branch: "Mumias", cash: 3572, float: 11060 },
  { branch: "Mutomo", cash: 10365, float: 29463 },
  { branch: "Mwatate", cash: 12668, float: 9057 },
  { branch: "Mwingi", cash: 4304, float: 15261 },
  { branch: "Nkubu", cash: 18900, float: 656 },
  { branch: "Nyamira", cash: 40006, float: 1896 },
  { branch: "Nyeri", cash: 28769, float: 20628 },
  { branch: "Oyugis", cash: 12518, float: 13794 },
  { branch: "Prudential", cash: 136521, float: 17602 }, // CORRECTED: actual Excel values
  { branch: "Sori", cash: 32, float: 2599 },
  { branch: "Voi", cash: 21765, float: 38879 }, // ADDED: was wrongly excluded
  { branch: "Webuye", cash: 39962, float: 17681 },
  { branch: "Wote", cash: 16041, float: 5890 },
  { branch: "Wundanyi", cash: 9830, float: 12458 }
];

// Branch name mapping for database lookup
const branchNameMapping = {
  "Mau Summit": "Mau summit", // Excel has "Mau Summit", DB has "Mau summit"
  "Mombasa": "Mombasa " // Excel has "Mombasa", DB has "Mombasa " (with space)
};

async function improvedUpdateClosingBalances() {
  const transaction = await sequelize.transaction();
  
  try {
    logger.info('🔄 IMPROVED UPDATE MODE - MAKING ACTUAL DATABASE CHANGES');
    logger.info('===========================================================');
    logger.info('📅 Updating corrected 27th May 2025 balances...');
    logger.info('🔧 Fixes: Includes Voi, corrects data discrepancies');
    logger.info('⚠️ WARNING: This will modify the database!');
    
    const results = {
      excelBranches: balancesDataFromExcel.length,
      foundInDB: 0,
      notFoundInDB: 0,
      hasSession: 0,
      noSession: 0,
      hasReconciliation: 0,
      noReconciliation: 0,
      correctValues: 0,
      updatedValues: 0,
      errors: [],
      updates: [],
      skipped: []
    };
    
    // Step 1: Get all branches and their 27th May sessions
    logger.info('\n📋 Step 1: Loading all branches and 27th May sessions...');
    
    const targetDate = '2025-05-27';
    const startOfDay = `${targetDate} 00:00:00`;
    const endOfDay = `${targetDate} 23:59:59`;
    
    const { Op } = require('sequelize');
    
    const allBranches = await Branch.findAll({
      where: { deleted_at: null },
      include: [
        {
          model: PosSession,
          where: {
            start_time: {
              [Op.between]: [startOfDay, endOfDay]
            },
            deleted_at: null
          },
          required: false,
          include: [
            {
              model: PosSessionReconciliation,
              as: 'reconciliation',
              required: false
            }
          ]
        }
      ]
    });
    
    logger.info(`📊 Found ${allBranches.length} total branches in database`);
    
    // Step 2: Process each Excel branch
    logger.info('\n📋 Step 2: Processing each branch from Excel data...');
    
    for (const excelEntry of balancesDataFromExcel) {
      const { branch: excelBranchName, cash: expectedCash, float: expectedFloat } = excelEntry;
      
      // Map branch name if needed
      const dbBranchName = branchNameMapping[excelBranchName] || excelBranchName;
      
      logger.info(`\n🏪 Processing: ${excelBranchName} ${dbBranchName !== excelBranchName ? `(DB: "${dbBranchName}")` : ''}`);
      logger.info(`   📋 Expected: Cash ${expectedCash.toLocaleString()}, Float ${expectedFloat.toLocaleString()}`);
      
      // Find branch in database
      const dbBranch = allBranches.find(b => b.name === dbBranchName);
      
      if (!dbBranch) {
        results.notFoundInDB++;
        results.errors.push(`Branch not found: ${dbBranchName}`);
        logger.error(`   ❌ Branch not found in database: "${dbBranchName}"`);
        continue;
      }
      
      results.foundInDB++;
      logger.info(`   ✅ Found in DB: ID ${dbBranch.id}`);
      
      // Check for 27th May sessions
      const may27Sessions = dbBranch.PosSessions || [];
      
      if (may27Sessions.length === 0) {
        results.noSession++;
        results.errors.push(`No sessions for ${excelBranchName} on 27th May`);
        logger.error(`   ❌ No sessions on 27th May`);
        continue;
      }
      
      results.hasSession++;
      logger.info(`   📋 Found ${may27Sessions.length} session(s) on 27th May`);
      
      // Process each session
      for (const session of may27Sessions) {
        logger.info(`\n   🔍 Session ID: ${session.id} (${session.start_time})`);
        
        const reconciliation = session.reconciliation;
        
        if (!reconciliation) {
          results.noReconciliation++;
          results.errors.push(`No reconciliation for session ${session.id} (${excelBranchName})`);
          logger.error(`      ❌ No reconciliation found`);
          continue;
        }
        
        results.hasReconciliation++;
        
        const currentCash = parseFloat(reconciliation.closing_cash_balance || 0);
        const currentFloat = parseFloat(reconciliation.closing_mpesa_float || 0);
        
        logger.info(`      📝 Current: Cash ${currentCash.toLocaleString()}, Float ${currentFloat.toLocaleString()}`);
        
        // Check if values match
        const cashMatches = Math.abs(currentCash - expectedCash) < 0.01;
        const floatMatches = Math.abs(currentFloat - expectedFloat) < 0.01;
        
        if (cashMatches && floatMatches) {
          results.correctValues++;
          logger.info(`      ✅ Values already correct - skipping`);
          results.skipped.push({
            branch: excelBranchName,
            sessionId: session.id,
            reason: 'Values already correct'
          });
          continue;
        }
        
        // Update the reconciliation
        logger.warn(`      🔄 UPDATING:`);
        if (!cashMatches) {
          logger.warn(`         💰 Cash: ${currentCash.toLocaleString()} → ${expectedCash.toLocaleString()} (Diff: ${(expectedCash - currentCash).toLocaleString()})`);
        }
        if (!floatMatches) {
          logger.warn(`         📱 Float: ${currentFloat.toLocaleString()} → ${expectedFloat.toLocaleString()} (Diff: ${(expectedFloat - currentFloat).toLocaleString()})`);
        }
        
        // Perform the update
        const updateData = {
          closing_cash_balance: expectedCash,
          closing_mpesa_float: expectedFloat,
          notes: reconciliation.notes 
            ? `${reconciliation.notes} | Updated from improved 27th balances.xlsx on ${new Date().toISOString()}`
            : `Updated from improved 27th balances.xlsx on ${new Date().toISOString()}`
        };
        
        await reconciliation.update(updateData, { transaction });
        
        results.updatedValues++;
        results.updates.push({
          branch: excelBranchName,
          sessionId: session.id,
          reconciliationId: reconciliation.id,
          oldCash: currentCash,
          newCash: expectedCash,
          oldFloat: currentFloat,
          newFloat: expectedFloat
        });
        
        logger.info(`      ✅ Successfully updated reconciliation ${reconciliation.id}`);
      }
    }
    
    // Commit the transaction
    await transaction.commit();
    
    // Summary Report
    logger.info('\n📊 IMPROVED UPDATE SUMMARY');
    logger.info('===========================');
    logger.info(`📋 Excel branches: ${results.excelBranches}`);
    logger.info(`✅ Found in DB: ${results.foundInDB}`);
    logger.info(`❌ Not found in DB: ${results.notFoundInDB}`);
    logger.info(`📅 Have 27th May sessions: ${results.hasSession}`);
    logger.info(`⚠️ Missing 27th May sessions: ${results.noSession}`);
    logger.info(`📝 Have reconciliations: ${results.hasReconciliation}`);
    logger.info(`➕ Missing reconciliations: ${results.noReconciliation}`);
    logger.info(`✅ Already correct: ${results.correctValues}`);
    logger.info(`🔄 Successfully updated: ${results.updatedValues}`);
    logger.info(`❌ Errors: ${results.errors.length}`);
    
    // Key Improvements
    logger.info('\n🔧 KEY IMPROVEMENTS APPLIED:');
    logger.info('============================');
    logger.info('✅ Added Voi branch (was wrongly excluded)');
    logger.info('✅ Corrected Mariakani 2 values (19,750 cash, 4,748 float)');
    logger.info('✅ Corrected Prudential values (136,521 cash, 17,602 float)');
    logger.info('✅ Fixed Mau Summit case sensitivity');
    logger.info('✅ Fixed Mombasa trailing space issue');
    
    // Detailed Results
    if (results.updates.length > 0) {
      logger.info('\n🔄 SUCCESSFUL UPDATES:');
      results.updates.forEach(update => {
        logger.info(`   • ${update.branch} (Session ${update.sessionId}):`);
        logger.info(`     Cash: ${update.oldCash.toLocaleString()} → ${update.newCash.toLocaleString()}`);
        logger.info(`     Float: ${update.oldFloat.toLocaleString()} → ${update.newFloat.toLocaleString()}`);
      });
    }
    
    if (results.errors.length > 0) {
      logger.info('\n❌ ERRORS ENCOUNTERED:');
      results.errors.forEach(error => {
        logger.error(`   • ${error}`);
      });
    }
    
    logger.info(`\n📊 TOTAL CHANGES APPLIED: ${results.updatedValues}`);
    logger.info('\n🎉 Improved update completed successfully!');
    logger.info('💡 All corrections have been applied to the database.');
    
    return results;
    
  } catch (error) {
    await transaction.rollback();
    logger.error('💥 Fatal error during improved update:', error);
    throw error;
  }
}

// Execute the improved update
if (require.main === module) {
  improvedUpdateClosingBalances()
    .then((results) => {
      logger.info('✅ Improved update execution completed');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('💥 Improved update execution failed:', error);
      process.exit(1);
    });
}

module.exports = { improvedUpdateClosingBalances };
