#!/usr/bin/env node

/**
 * ACTUAL UPDATE RUNNER - MAKES REAL DATABASE CHANGES
 * 
 * ⚠️ WARNING: This script makes REAL database changes!
 * 
 * This script executes the actual closing balances update that:
 * 1. Creates missing POS sessions for branches without sessions on 27th May
 * 2. Updates existing reconciliation records with new closing balances
 * 3. Creates new reconciliation records for sessions without reconciliations
 * 
 * SAFETY FEATURES:
 * - All operations wrapped in a database transaction
 * - Automatic rollback on any error
 * - Detailed logging of all changes
 * - Comprehensive error handling
 * 
 * PREREQUISITES:
 * - Run preview script first: node data/cash-float/run-enhanced-preview.js
 * - Verify all changes look correct in the preview
 * - Ensure database backup is available
 * 
 * Run with: node data/cash-float/run-actual-update.js
 */

const path = require('path');
const readline = require('readline');

// Set up environment
require('dotenv').config({ path: path.join(__dirname, '../../.env') });

// Import the actual update function
const { updateClosingBalancesActual } = require('./update-closing-balances-actual');

// Create readline interface for user confirmation
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function askConfirmation(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer.toLowerCase().trim());
    });
  });
}

async function runWithConfirmation() {
  console.log('🚨 ACTUAL DATABASE UPDATE - REAL CHANGES WILL BE MADE');
  console.log('=====================================================');
  console.log('⚠️  This script will make REAL changes to the production database!');
  console.log('📊 Expected changes based on preview:');
  console.log('   • 1 new POS session (Emali branch)');
  console.log('   • 57 reconciliation updates');
  console.log('   • 9 new reconciliations');
  console.log('   • Total: 67 database changes');
  console.log('');
  console.log('🔒 SAFETY FEATURES:');
  console.log('   • All changes in a single transaction');
  console.log('   • Automatic rollback on any error');
  console.log('   • Detailed logging of all operations');
  console.log('');
  console.log('📋 PREREQUISITES CHECKLIST:');
  console.log('   ✅ Preview script run and reviewed');
  console.log('   ✅ Database backup available');
  console.log('   ✅ Changes validated and approved');
  console.log('');
  
  // First confirmation
  const confirm1 = await askConfirmation('❓ Have you run the preview script and reviewed all changes? (yes/no): ');
  if (confirm1 !== 'yes') {
    console.log('❌ Please run the preview script first: node data/cash-float/run-enhanced-preview.js');
    rl.close();
    process.exit(1);
  }
  
  // Second confirmation
  const confirm2 = await askConfirmation('❓ Do you have a recent database backup? (yes/no): ');
  if (confirm2 !== 'yes') {
    console.log('❌ Please ensure you have a database backup before proceeding.');
    rl.close();
    process.exit(1);
  }
  
  // Final confirmation
  console.log('');
  console.log('🚨 FINAL CONFIRMATION REQUIRED');
  console.log('==============================');
  const confirm3 = await askConfirmation('❓ Are you absolutely sure you want to proceed with REAL database changes? Type "PROCEED" to continue: ');
  if (confirm3 !== 'proceed') {
    console.log('❌ Update cancelled. No changes made.');
    rl.close();
    process.exit(1);
  }
  
  rl.close();
  
  console.log('');
  console.log('🚀 STARTING ACTUAL DATABASE UPDATE...');
  console.log('=====================================');
  
  try {
    const results = await updateClosingBalancesActual();
    
    console.log('');
    console.log('🎉 UPDATE COMPLETED SUCCESSFULLY!');
    console.log('=================================');
    console.log(`📊 SUMMARY:`);
    console.log(`   • Sessions created: ${results.sessionsCreated.length}`);
    console.log(`   • Reconciliations updated: ${results.reconciliationsUpdated.length}`);
    console.log(`   • Reconciliations created: ${results.reconciliationsCreated.length}`);
    console.log(`   • Errors: ${results.errors.length}`);
    console.log(`   • Warnings: ${results.warnings.length}`);
    
    const totalChanges = results.sessionsCreated.length + results.reconciliationsUpdated.length + results.reconciliationsCreated.length;
    console.log(`   • Total changes: ${totalChanges}`);
    
    if (results.errors.length > 0) {
      console.log('\n⚠️ Some errors occurred, but successful changes were committed.');
      console.log('Check the detailed logs above for more information.');
    }
    
    console.log('\n✅ All changes have been successfully committed to the database.');
    console.log('💡 You can verify the changes by running queries on the affected tables.');
    
    process.exit(0);
    
  } catch (error) {
    console.error('\n💥 UPDATE FAILED!');
    console.error('==================');
    console.error('❌ Error:', error.message);
    console.error('🔄 All changes have been automatically rolled back.');
    console.error('💾 No changes were made to the database.');
    console.error('\nFull error details:');
    console.error(error.stack);
    
    process.exit(1);
  }
}

// Execute with confirmation
runWithConfirmation().catch((error) => {
  console.error('💥 Script execution failed:', error);
  rl.close();
  process.exit(1);
});
