#!/usr/bin/env node

/**
 * FIX 28TH MAY OPENING BALANCES RUNNER
 * 
 * This script provides an interactive interface for fixing 28th May opening balances
 * to match the 27th May closing balances we just corrected.
 * 
 * MODES:
 * - PREVIEW: Shows what would be fixed (NO DATABASE CHANGES)
 * - EXECUTE: Actually fixes the opening balances (MODIFIES DATABASE)
 * 
 * Usage:
 * - node data/cash-float/run-fix-opening-balances.js preview
 * - node data/cash-float/run-fix-opening-balances.js execute
 */

const path = require('path');
const readline = require('readline');

// Set up environment
require('dotenv').config({ path: path.join(__dirname, '../../.env') });

// Import the fix function
const { fixOpeningBalances28th } = require('./fix-opening-balances-28th');

// Create readline interface for user confirmation
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer);
    });
  });
}

async function main() {
  const mode = process.argv[2]?.toUpperCase() || 'PREVIEW';
  
  if (!['PREVIEW', 'EXECUTE'].includes(mode)) {
    console.log('❌ Invalid mode. Usage:');
    console.log('   node data/cash-float/run-fix-opening-balances.js preview');
    console.log('   node data/cash-float/run-fix-opening-balances.js execute');
    process.exit(1);
  }
  
  console.log('🔧 FIX 28TH MAY OPENING BALANCES');
  console.log('================================');
  
  if (mode === 'PREVIEW') {
    console.log('👁️ PREVIEW MODE - NO DATABASE CHANGES');
    console.log('📋 This will show what opening balances would be fixed');
  } else {
    console.log('🚨 EXECUTE MODE - ACTUAL DATABASE CHANGES');
    console.log('⚠️ WARNING: This will modify 28th May session opening balances!');
  }
  
  console.log('\n🎯 PROBLEM:');
  console.log('   • We updated 27th May closing balances');
  console.log('   • Those closing balances should be 28th May opening balances');
  console.log('   • Some 28th May sessions have incorrect opening balances');
  
  console.log('\n🔧 TARGET SESSIONS WITH INCORRECT OPENING BALANCES:');
  console.log('   • Kathonzweni: 690,104/453,685 → 29,304/935');
  console.log('   • Kilgoris: 33,748/-148,941 → 30,923/11,159');
  console.log('   • Malaba: 174,598/11,018 → 21,463/24,168');
  console.log('   • Mariakani 2: 4,748/19,750 → 19,750/4,748 (swap)');
  console.log('   • Mombasa: 0/0 → 34,821/96,381');
  console.log('   • Prudential: 54,726/10,102 → 136,521/17,602');
  console.log('   • Voi: 44,339/-138,266 → 21,765/38,879');
  
  console.log('\n✅ STRATEGY:');
  console.log('   • Update 28th May opening balances to match 27th May closing balances');
  console.log('   • Ensure proper cash flow continuity between days');
  console.log('   • Fix negative and extreme values');
  
  if (mode === 'EXECUTE') {
    console.log('\n⚠️ PRODUCTION DATABASE WARNING:');
    console.log('   • This will modify active 28th May sessions');
    console.log('   • Ensure you have recent database backups');
    console.log('   • Run PREVIEW mode first to verify the changes');
    
    const answer = await askQuestion('\n❓ Do you want to proceed with EXECUTE mode? (yes/no): ');
    
    if (answer.toLowerCase() !== 'yes' && answer.toLowerCase() !== 'y') {
      console.log('\n❌ Opening balance fix cancelled by user.');
      rl.close();
      process.exit(0);
    }
    
    const confirmAnswer = await askQuestion('\n❓ Are you absolutely sure? This will modify active sessions (yes/no): ');
    
    if (confirmAnswer.toLowerCase() !== 'yes' && confirmAnswer.toLowerCase() !== 'y') {
      console.log('\n❌ Opening balance fix cancelled by user.');
      rl.close();
      process.exit(0);
    }
  }
  
  console.log(`\n🚀 Starting opening balance fix in ${mode} mode...`);
  console.log('⏳ This may take a few moments...\n');
  
  try {
    const results = await fixOpeningBalances28th(mode);
    
    console.log(`\n✅ Opening balance fix ${mode.toLowerCase()} completed successfully!`);
    
    console.log('\n📊 FINAL SUMMARY:');
    console.log(`   • Sessions analyzed: ${results.sessionsAnalyzed}`);
    console.log(`   • Sessions already correct: ${results.sessionsAlreadyCorrect}`);
    console.log(`   • Sessions ${mode === 'PREVIEW' ? 'to fix' : 'fixed'}: ${results.updates.length}`);
    console.log(`   • Sessions not found: ${results.sessionsNotFound}`);
    console.log(`   • Errors encountered: ${results.errors.length}`);
    
    if (mode === 'PREVIEW') {
      console.log('\n👁️ PREVIEW RESULTS:');
      if (results.updates.length > 0) {
        console.log(`   • ${results.updates.length} sessions need opening balance fixes`);
        console.log('   • All fixes align 28th May opening with 27th May closing balances');
        console.log('   • This will resolve negative values and extreme amounts');
        
        console.log('\n🔧 NEXT STEPS:');
        console.log('   1. Review the detailed logs above');
        console.log('   2. Verify the opening balance fixes are correct');
        console.log('   3. Run in EXECUTE mode when ready:');
        console.log('      node data/cash-float/run-fix-opening-balances.js execute');
      } else {
        console.log('   ✅ No opening balance fixes needed - all data is correct!');
        console.log('   🎉 28th May opening balances already match 27th May closing balances');
      }
    } else {
      console.log('\n🎉 OPENING BALANCE FIXES COMPLETED SUCCESSFULLY!');
      if (results.updates.length > 0) {
        console.log(`   ✅ Fixed ${results.updates.length} sessions with incorrect opening balances`);
        console.log('   ✅ 28th May opening balances now match 27th May closing balances');
        console.log('   ✅ Proper cash flow continuity established between days');
        console.log('   ✅ Negative values and extreme amounts resolved');
        
        console.log('\n🔍 VERIFICATION RECOMMENDED:');
        console.log('   1. Check that all 28th May sessions have correct opening balances');
        console.log('   2. Verify cash flow continuity from 27th to 28th May');
        console.log('   3. Confirm no negative or extreme values remain');
      } else {
        console.log('   ✅ No changes were needed - opening balances were already correct');
      }
    }
    
    if (results.errors.length > 0) {
      console.log('\n⚠️ ERRORS ENCOUNTERED:');
      results.errors.forEach(error => {
        console.log(`   • ${error}`);
      });
      console.log('\n🔧 Please review and resolve errors before proceeding');
    }
    
  } catch (error) {
    console.error(`\n💥 Opening balance fix ${mode.toLowerCase()} failed:`, error.message);
    
    if (mode === 'EXECUTE') {
      console.error('🔄 All changes have been rolled back');
      console.error('📋 Database remains unchanged');
    }
    
    if (error.stack) {
      console.error('\n📝 Error details:');
      console.error(error.stack);
    }
    
    rl.close();
    process.exit(1);
  }
  
  rl.close();
  process.exit(0);
}

// Run the main function
main().catch((error) => {
  console.error('💥 Fatal error:', error);
  rl.close();
  process.exit(1);
});
