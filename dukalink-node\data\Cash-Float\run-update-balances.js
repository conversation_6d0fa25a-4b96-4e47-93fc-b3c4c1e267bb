#!/usr/bin/env node

/**
 * Simple runner script for updating closing balances
 * Run with: node data/cash-float/run-update-balances.js
 */

const path = require('path');

// Set up environment
require('dotenv').config({ path: path.join(__dirname, '../../.env') });

// Import and run the update function
const { updateClosingBalances } = require('./update-closing-balances');

console.log('🚀 Starting 27th balances update...');
console.log('=====================================');

updateClosingBalances()
  .then(() => {
    console.log('\n✅ Update completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 Update failed:', error.message);
    console.error(error.stack);
    process.exit(1);
  });
