"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { useIsAuthenticated } from "@/features/auth/hooks/use-auth";
import { formatDate } from "@/lib/utils";
import { ClipboardEdit, Eye, Loader2, FileSpreadsheet } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { useBranches } from "@/features/branches/hooks/use-branches";
import { useDsaStockAssignmentsByIdentifier } from "../hooks/use-dsa-stock-assignments";
import { exportDsaAssignmentsToExcel } from "../utils/export-dsa-assignments";

export function StockAssignmentsByIdentifierList() {
  const router = useRouter();
  const { user } = useIsAuthenticated();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedBranchId, setSelectedBranchId] = useState<number | undefined>(
    user?.role_name === "branch_manager"
      ? user.branch_id || undefined
      : undefined
  );

  // Fetch branches for filtering
  const { data: branches } = useBranches();

  // Fetch stock assignments grouped by identifier
  const { data: groupedAssignments, isLoading } = useDsaStockAssignmentsByIdentifier({
    branch_id: selectedBranchId,
  });

  console.log("Fetched assignments:", groupedAssignments);

  // Debug DSA agent names
  if (groupedAssignments && groupedAssignments.length > 0) {
    console.log("First assignment group:", groupedAssignments[0]);
    console.log("First item in first group:", groupedAssignments[0]?.items?.[0]);
    console.log("User in first item:", groupedAssignments[0]?.items?.[0]?.User);

    // Check for groups with missing agent names
    const missingAgentNames = groupedAssignments.filter(
      group => !group.dsa_agent_name && (!group.items?.[0]?.User?.name)
    );

    if (missingAgentNames.length > 0) {
      console.warn(`Found ${missingAgentNames.length} groups with missing agent names`);
      console.warn("First group with missing agent name:", missingAgentNames[0]);
    }
  }

  // Filter assignments by search term
  const filteredAssignments = groupedAssignments?.filter(
    (group) =>
      group.dsa_agent_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      group.branch_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      group.assignment_identifier?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Handle Excel export
  const handleExportToExcel = () => {
    if (!filteredAssignments || filteredAssignments.length === 0) {
      return;
    }

    try {
      exportDsaAssignmentsToExcel(filteredAssignments, "dsa-assignments-export");
    } catch (error) {
      console.error("Error exporting DSA assignments:", error);
    }
  };

  return (
    <Card>
      <CardHeader className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
        <div>
          <CardTitle>DSA Invoices</CardTitle>
          <CardDescription>
            Manage stock assignments to DSA agents
          </CardDescription>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={handleExportToExcel}
            disabled={isLoading || !filteredAssignments || filteredAssignments.length === 0}
          >
            <FileSpreadsheet className="h-4 w-4 mr-2" />
            Export to Excel
          </Button>
          <Button onClick={() => router.push("/dsa/assignments/create")}>
            Assign Stock
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="mb-4 flex flex-col space-y-4 sm:flex-row sm:items-center sm:space-x-4 sm:space-y-0">
          <div className="flex-1">
            <Input
              placeholder="Search assignments..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          {user?.role_name !== "branch_manager" && (
            <div className="w-full sm:w-[200px]">
              <Select
                value={selectedBranchId?.toString() || "all"}
                onValueChange={(value) =>
                  setSelectedBranchId(
                    value !== "all" ? parseInt(value, 10) : undefined
                  )
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="All Branches" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Branches</SelectItem>
                  {branches?.data?.map((branch) => (
                    <SelectItem key={branch.id} value={branch.id.toString()}>
                      {branch.name} {branch.level === 0 && "(Headquarters)"}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}
        </div>

        {isLoading ? (
          <div className="space-y-2">
            {Array.from({ length: 5 }).map((_, index) => (
              <div key={index} className="flex items-center space-x-4">
                <Skeleton className="h-12 w-full" />
              </div>
            ))}
          </div>
        ) : filteredAssignments?.length === 0 ? (
          <div className="flex h-[300px] items-center justify-center rounded-lg border border-dashed">
            <div className="text-center">
              <p className="text-sm text-muted-foreground">
                No stock assignments found
              </p>
            </div>
          </div>
        ) : (
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Assignment ID</TableHead>
                  <TableHead>DSA Agent</TableHead>
                  <TableHead>Branch</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="w-[100px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredAssignments?.map((group) => (
                  <TableRow key={group.assignment_identifier}>
                    <TableCell className="font-medium">
                      {group.assignment_identifier}
                    </TableCell>
                    <TableCell>{group.items?.[0]?.User?.name || group.dsa_agent_name || "N/A"}</TableCell>
                    <TableCell>{group.items?.[0]?.Branch?.name || group.branch_name || "N/A"}</TableCell>
                    <TableCell>
                      {formatDate(group.created_at)}
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant={group.reconciled ? "outline" : "default"}
                        className={
                          group.reconciled
                            ? "bg-green-100 text-green-800"
                            : "bg-yellow-100 text-yellow-800"
                        }
                      >
                        {group.reconciled ? "Reconciled" : "Unreconciled"}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {group.reconciled ? (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() =>
                            router.push(`/dsa/assignments/view/${group.assignment_identifier}`)
                          }
                        >
                          <Eye className="mr-2 h-4 w-4" />
                          View
                        </Button>
                      ) : (
                        <Button
                          variant="default"
                          size="sm"
                          onClick={() =>
                            router.push(`/dsa/assignments/reconcile/${group.assignment_identifier}`)
                          }
                        >
                          <ClipboardEdit className="mr-2 h-4 w-4" />
                          Reconcile
                        </Button>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
