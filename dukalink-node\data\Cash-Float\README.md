# Cash Float Balance Update Scripts

This directory contains scripts to update closing cash and M-Pesa float balances for POS sessions based on Excel data from 27th May 2025.

## Files

### Preview Scripts (Safe - No Database Changes)
- `preview-closing-balances.js` - Basic preview of reconciliation updates
- `preview-with-session-creation.js` - Enhanced preview including session creation
- `run-preview.js` - Runner for basic preview
- `run-enhanced-preview.js` - Runner for enhanced preview

### Update Scripts (Makes Real Database Changes)
- `update-closing-balances-actual.js` - Main script that makes real database changes
- `run-actual-update.js` - Interactive runner with safety confirmations
- `verify-update-results.js` - Verification script to check results after update

### Legacy Scripts
- `update-closing-balances.js` - Original script (use actual version instead)
- `run-update-balances.js` - Original runner (use actual version instead)

### Data Files
- `27th balances.xlsx` - Excel file containing the balance data

## How it Works

The update process follows these steps:

1. **Branch Validation**: Validates all branch names from Excel against database
2. **Session Analysis**: Finds all POS sessions from 27th May 2025
3. **Session Creation**: Creates missing POS sessions for branches without sessions
4. **Reconciliation Updates**: Updates existing reconciliation records
5. **Reconciliation Creation**: Creates new reconciliation records for sessions without them

### Key Features
- **Session Creation**: Automatically creates POS sessions for branches missing sessions on 27th May
- **Multiple Sessions**: Handles branches with multiple sessions on the same day
- **Transaction Safety**: All operations wrapped in database transactions
- **Comprehensive Logging**: Detailed logging of all operations
- **Error Handling**: Automatic rollback on any errors

## Usage Workflow

### Step 1: Run Enhanced Preview (REQUIRED)
```bash
node data/cash-float/run-enhanced-preview.js
```
This shows exactly what will be changed without making any database modifications.

### Step 2: Review Preview Results
Carefully review the preview output to ensure all changes are correct:
- Sessions to be created
- Reconciliations to be updated
- Reconciliations to be created
- Any warnings or errors

### Step 3: Run Actual Update (DANGER - MAKES REAL CHANGES)
```bash
node data/cash-float/run-actual-update.js
```
This script includes multiple safety confirmations and makes real database changes.

### Step 4: Verify Results
```bash
node data/cash-float/verify-update-results.js
```
This verifies that all changes were applied correctly.

## Data Structure

The script processes the following data from the 27th balances Excel file:

| Branch Name | Cash Balance | Float Balance |
|-------------|--------------|---------------|
| Bungoma     | 13,449       | 10,257        |
| Busia       | 17,363       | 14,519        |
| Chuka       | 21,801       | 1,928         |
| ...         | ...          | ...           |

## Database Updates

The script updates the `pos_session_reconciliations` table:

- **If reconciliation exists**: Updates `closing_cash_balance` and `closing_mpesa_float`
- **If no reconciliation**: Creates new record with all required fields

## Error Handling

The script handles common errors:

- Branch not found in database
- No POS session found for branch
- Database transaction failures

All operations are wrapped in a database transaction for data integrity.

## Logging

The script provides detailed logging:

- ✅ Successful updates
- ❌ Errors and warnings
- 📊 Summary report with counts

## Safety Features

- **Transaction-based**: All updates in a single transaction
- **Rollback on error**: Automatic rollback if any operation fails
- **Validation**: Checks for branch existence and POS sessions
- **Detailed logging**: Full audit trail of all operations
