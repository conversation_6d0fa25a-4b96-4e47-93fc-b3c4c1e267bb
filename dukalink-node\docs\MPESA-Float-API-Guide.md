# MPESA Float Management API Guide

This guide provides comprehensive documentation for all MPESA float and float reconciliation endpoints in the DukaLink system.

## Base URL
All endpoints are prefixed with: `/api/v1`

## Authentication
All endpoints require Bearer token authentication:
```
Authorization: Bearer <your-jwt-token>
```

---

## 1. MPESA Float Reconciliations

### 1.1 Get All MPESA Float Reconciliations
**GET** `/mpesa-float-reconciliations`

View and manage MPESA float reconciliations with filtering options.

**Query Parameters:**
- `user_id` (integer, optional) - Filter by user ID
- `branch_id` (integer, optional) - Filter by branch ID
- `start_date` (string, optional) - Filter by start date (YYYY-MM-DD)
- `end_date` (string, optional) - Filter by end date (YYYY-MM-DD)

**Response (200 OK):**
```json
[
  {
    "id": 1,
    "branch_id": 2,
    "user_id": 3,
    "float_assigned": 50000.00,
    "total_deposits": 25000.00,
    "total_withdrawals": 15000.00,
    "closing_balance": 60000.00,
    "closing_mpesa_float": 60000.00,
    "discrepancies": 0.00,
    "notes": "End of day reconciliation",
    "reconciled_at": "2023-01-01T00:00:00.000Z",
    "created_by": 3,
    "last_updated_by": 3,
    "created_at": "2023-01-01T00:00:00.000Z",
    "updated_at": "2023-01-01T00:00:00.000Z"
  }
]
```

### 1.2 Get MPESA Float Reconciliation by ID
**GET** `/mpesa-float-reconciliations/{id}`

**Path Parameters:**
- `id` (integer, required) - MPESA float reconciliation ID

**Response (200 OK):** Same as single reconciliation object above

### 1.3 Get Reconciliations by User ID
**GET** `/mpesa-float-reconciliations/user/{userId}`

**Path Parameters:**
- `userId` (integer, required) - User ID

**Response (200 OK):** Array of reconciliation objects for the specified user

### 1.4 Get Reconciliations by Branch ID
**GET** `/mpesa-float-reconciliations/branch/{branchId}`

**Path Parameters:**
- `branchId` (integer, required) - Branch ID

**Response (200 OK):** Array of reconciliation objects for the specified branch

### 1.5 Create MPESA Float Reconciliation
**POST** `/mpesa-float-reconciliations`

**Request Body:**
```json
{
  "user_id": 3,
  "branch_id": 2,
  "float_assigned": 50000.00,
  "total_deposits": 25000.00,
  "total_withdrawals": 15000.00,
  "closing_balance": 60000.00,
  "closing_mpesa_float": 60000.00,
  "discrepancies": 0.00,
  "notes": "End of day reconciliation"
}
```

**Required Fields:**
- `user_id`, `branch_id`, `float_assigned`, `total_deposits`, `total_withdrawals`, `closing_balance`

**Response (201 Created):** Created reconciliation object

### 1.6 Update MPESA Float Reconciliation
**PUT** `/mpesa-float-reconciliations/{id}`

**Path Parameters:**
- `id` (integer, required) - MPESA float reconciliation ID

**Request Body:** Same as create, but all fields optional

**Response (200 OK):** Updated reconciliation object

### 1.7 Delete MPESA Float Reconciliation
**DELETE** `/mpesa-float-reconciliations/{id}`

**Path Parameters:**
- `id` (integer, required) - MPESA float reconciliation ID

**Response (200 OK):** Success message

---

## 2. MPESA Float Balances

### 2.1 Get All MPESA Float Balances
**GET** `/mpesa-float-balances`

**Query Parameters:**
- `branch_id` (string, optional) - Filter by branch ID or use 'hq' for HQ balance
- `include_hq` (boolean, optional) - Include HQ balance in results
- `tenant_id` (integer, optional) - Filter by tenant ID

**Response (200 OK):**
```json
[
  {
    "id": 1,
    "branch_id": 1,
    "tenant_id": 1,
    "current_balance": 5000.00,
    "last_updated": "2023-01-01T00:00:00.000Z",
    "updated_by": 1,
    "created_at": "2023-01-01T00:00:00.000Z",
    "updated_at": "2023-01-01T00:00:00.000Z"
  }
]
```

### 2.2 Get MPESA Float Balance by Branch ID
**GET** `/mpesa-float-balances/branch/{branchId}`

**Path Parameters:**
- `branchId` (string, required) - Branch ID or 'hq' for HQ balance

**Query Parameters:**
- `tenant_id` (integer, required when branchId is 'hq') - Tenant ID for HQ balance

**Response (200 OK):** Single balance object

---

## 3. MPESA Float Movements

### 3.1 Get All MPESA Float Movements
**GET** `/mpesa-float-movements`

**Query Parameters:**
- `tenant_id` (integer, optional) - Filter by tenant ID
- `from_branch_id` (integer, optional) - Filter by source branch ID
- `from_hq` (boolean, optional) - Filter for movements from HQ
- `to_branch_id` (integer, optional) - Filter by destination branch ID
- `status` (string, optional) - Filter by status: pending, in_transit, received, cancelled
- `start_date` (string, optional) - Filter by start date (YYYY-MM-DD)
- `end_date` (string, optional) - Filter by end date (YYYY-MM-DD)
- `page` (integer, optional, default: 1) - Page number
- `limit` (integer, optional, default: 20) - Items per page

**Response (200 OK):**
```json
{
  "data": [
    {
      "id": 1,
      "tenant_id": 1,
      "from_branch_id": null,
      "to_branch_id": 3,
      "amount": 10000.00,
      "status": "received",
      "notes": "HQ to branch transfer",
      "created_by": 1,
      "last_updated_by": 1,
      "created_at": "2023-01-01T00:00:00.000Z",
      "updated_at": "2023-01-01T00:00:00.000Z"
    }
  ],
  "pagination": {
    "total": 50,
    "page": 1,
    "limit": 20,
    "pages": 3
  }
}
```

### 3.2 Get MPESA Float Movement by ID
**GET** `/mpesa-float-movements/{id}`

**Path Parameters:**
- `id` (integer, required) - MPESA Float Movement ID

**Response (200 OK):** Single movement object

### 3.3 Create MPESA Float Movement
**POST** `/mpesa-float-movements`

**Request Body:**
```json
{
  "tenant_id": 1,
  "from_branch_id": null,
  "to_branch_id": 3,
  "amount": 10000.00,
  "notes": "HQ to branch transfer"
}
```

**Required Fields:**
- `tenant_id`, `to_branch_id`, `amount`

**Note:** Set `from_branch_id` to `null` for HQ to branch movements

**Response (201 Created):** Created movement object

### 3.4 Update MPESA Float Movement Status
**PUT** `/mpesa-float-movements/{id}/status`

**Path Parameters:**
- `id` (integer, required) - MPESA Float Movement ID

**Request Body:**
```json
{
  "status": "received",
  "notes": "Additional notes"
}
```

**Required Fields:**
- `status` (pending, in_transit, received, cancelled)

**Response (200 OK):** Updated movement object

### 3.5 Delete MPESA Float Movement
**DELETE** `/mpesa-float-movements/{id}`

**Path Parameters:**
- `id` (integer, required) - MPESA Float Movement ID

**Response (200 OK):** Success message

**Note:** Can only delete movements with 'pending' status

### 3.6 Get HQ MPESA Float Movement Statistics
**GET** `/mpesa-float-movements/hq/stats`

**Query Parameters:**
- `tenant_id` (integer, optional) - Filter by tenant ID
- `start_date` (string, optional) - Filter by start date (YYYY-MM-DD)
- `end_date` (string, optional) - Filter by end date (YYYY-MM-DD)

**Response (200 OK):**
```json
{
  "total_movements": 25,
  "status_breakdown": {
    "pending": 2,
    "in_transit": 3,
    "received": 18,
    "cancelled": 2
  },
  "total_amount_sent": 500000.00,
  "total_amount_received": 480000.00,
  "discrepancy": 20000.00,
  "top_branches": [
    {
      "branch_id": 3,
      "branch_name": "Branch A",
      "total_received": 150000.00,
      "movement_count": 8
    }
  ]
}
```

---

## 4. MPESA Float Top-ups

### 4.1 Create MPESA Float Top-up
**POST** `/mpesa-float-topups`

**Request Body:**
```json
{
  "tenant_id": 1,
  "amount": 50000.00,
  "payment_method": "bank_transfer",
  "transaction_reference": "BT123456789",
  "notes": "Monthly MPESA float top-up"
}
```

**Required Fields:**
- `tenant_id`, `amount`, `payment_method`

**Response (201 Created):**
```json
{
  "id": 1,
  "tenant_id": 1,
  "reference_number": "MPESA-TOPUP-**********-1234",
  "amount": 50000.00,
  "payment_method": "bank_transfer",
  "transaction_reference": "BT123456789",
  "balance_before": 10000.00,
  "balance_after": 60000.00,
  "status": "completed",
  "notes": "Monthly MPESA float top-up",
  "created_by": 1,
  "last_updated_by": 1,
  "created_at": "2023-01-01T00:00:00.000Z",
  "updated_at": "2023-01-01T00:00:00.000Z"
}
```

### 4.2 Get All MPESA Float Top-ups
**GET** `/mpesa-float-topups`

**Query Parameters:**
- `tenant_id` (integer, optional) - Filter by tenant ID
- `payment_method` (string, optional) - Filter by payment method
- `status` (string, optional) - Filter by status: pending, completed, cancelled
- `start_date` (string, optional) - Filter by start date (YYYY-MM-DD)
- `end_date` (string, optional) - Filter by end date (YYYY-MM-DD)
- `page` (integer, optional, default: 1) - Page number
- `limit` (integer, optional, default: 20) - Items per page

**Response (200 OK):**
```json
{
  "data": [
    {
      "id": 1,
      "tenant_id": 1,
      "reference_number": "MPESA-TOPUP-**********-1234",
      "amount": 50000.00,
      "payment_method": "bank_transfer",
      "transaction_reference": "BT123456789",
      "balance_before": 10000.00,
      "balance_after": 60000.00,
      "status": "completed",
      "notes": "Monthly MPESA float top-up",
      "created_by": 1,
      "last_updated_by": 1,
      "created_at": "2023-01-01T00:00:00.000Z",
      "updated_at": "2023-01-01T00:00:00.000Z"
    }
  ],
  "pagination": {
    "total": 15,
    "page": 1,
    "limit": 20,
    "pages": 1
  }
}
```

### 4.3 Get MPESA Float Top-up Statistics
**GET** `/mpesa-float-topups/stats`

**Query Parameters:**
- `tenant_id` (integer, optional) - Filter by tenant ID
- `start_date` (string, optional) - Filter by start date (YYYY-MM-DD)
- `end_date` (string, optional) - Filter by end date (YYYY-MM-DD)

**Response (200 OK):**
```json
{
  "total_amount": 500000.00,
  "total_count": 10,
  "by_payment_method": [
    {
      "payment_method": "bank_transfer",
      "total_amount": 300000.00,
      "count": 6
    },
    {
      "payment_method": "mpesa",
      "total_amount": 200000.00,
      "count": 4
    }
  ],
  "recent_topups": [
    {
      "id": 1,
      "amount": 50000.00,
      "payment_method": "bank_transfer",
      "status": "completed",
      "created_at": "2023-01-01T00:00:00.000Z"
    }
  ]
}
```

### 4.4 Get MPESA Float Top-up by ID
**GET** `/mpesa-float-topups/{id}`

**Path Parameters:**
- `id` (integer, required) - MPESA float top-up ID

**Response (200 OK):** Single top-up object

### 4.5 Update MPESA Float Top-up Status
**PUT** `/mpesa-float-topups/{id}/status`

**Path Parameters:**
- `id` (integer, required) - MPESA float top-up ID

**Request Body:**
```json
{
  "status": "completed",
  "notes": "Additional notes for status update"
}
```

**Required Fields:**
- `status` (pending, completed, cancelled)

**Response (200 OK):** Updated top-up object

### 4.6 Delete MPESA Float Top-up
**DELETE** `/mpesa-float-topups/{id}`

**Path Parameters:**
- `id` (integer, required) - MPESA float top-up ID

**Response (200 OK):**
```json
{
  "message": "MPESA float top-up deleted successfully"
}
```

---

## 5. POS Session Reconciliations (Float Related)

### 5.1 Get All POS Session Reconciliations
**GET** `/pos-session-reconciliations`

**Query Parameters:**
- `pos_session_id` (integer, optional) - Filter by POS session ID
- `start_date` (string, optional) - Filter by start date (YYYY-MM-DD)
- `end_date` (string, optional) - Filter by end date (YYYY-MM-DD)

**Response (200 OK):**
```json
[
  {
    "id": 1,
    "pos_session_id": 1,
    "closing_cash_balance": 15000.00,
    "closing_mpesa_balance": 8000.00,
    "closing_mpesa_float": 12000.00,
    "total_sales": 50000.00,
    "discrepancies": 0.00,
    "notes": "End of day closing",
    "created_at": "2023-01-01T08:00:00.000Z",
    "created_by": 3,
    "last_updated_by": 3
  }
]
```

### 5.2 Get POS Session Reconciliation by ID
**GET** `/pos-session-reconciliations/{id}`

**Path Parameters:**
- `id` (integer, required) - POS session reconciliation ID

**Response (200 OK):** Single reconciliation object

### 5.3 Get Reconciliation by Session ID
**GET** `/pos-session-reconciliations/session/{sessionId}`

**Path Parameters:**
- `sessionId` (integer, required) - POS session ID

**Response (200 OK):** Single reconciliation object for the specified session

### 5.4 Create POS Session Reconciliation
**POST** `/pos-session-reconciliations`

**Request Body:**
```json
{
  "pos_session_id": 1,
  "closing_cash_balance": 15000.00,
  "closing_mpesa_balance": 8000.00,
  "closing_mpesa_float": 12000.00,
  "total_sales": 50000.00,
  "discrepancies": 0.00,
  "notes": "End of day closing"
}
```

**Required Fields:**
- `pos_session_id`, `closing_cash_balance`, `total_sales`

**Response (201 Created):** Created reconciliation object

### 5.5 Update POS Session Reconciliation
**PUT** `/pos-session-reconciliations/{id}`

**Path Parameters:**
- `id` (integer, required) - POS session reconciliation ID

**Request Body:** Same as create, but all fields optional

**Response (200 OK):** Updated reconciliation object

### 5.6 Delete POS Session Reconciliation
**DELETE** `/pos-session-reconciliations/{id}`

**Path Parameters:**
- `id` (integer, required) - POS session reconciliation ID

**Response (200 OK):** Success message

### 5.7 Get Reconciliation Summary
**GET** `/pos-session-reconciliations/summary`

**Query Parameters:**
- `start_date` (string, required) - Start date (YYYY-MM-DD)
- `end_date` (string, required) - End date (YYYY-MM-DD)
- `branch_id` (integer, optional) - Filter by branch ID

**Response (200 OK):**
```json
{
  "total_sessions": 25,
  "total_sales": 1250000.00,
  "total_cash_in": 800000.00,
  "total_cash_out": 50000.00,
  "total_discrepancies": 1500.00,
  "sessions_by_branch": [
    {
      "branch_id": 1,
      "branch_name": "Main Branch",
      "session_count": 15,
      "total_sales": 750000.00,
      "total_discrepancies": 500.00
    }
  ],
  "sessions_by_user": [
    {
      "user_id": 3,
      "user_name": "John Doe",
      "session_count": 10,
      "total_sales": 500000.00,
      "total_discrepancies": 200.00
    }
  ]
}
```

### 5.8 Get Last Reconciliation for Branch
**GET** `/pos-session-reconciliations/branch/{branch_id}/last`

**Path Parameters:**
- `branch_id` (integer, required) - Branch ID

**Response (200 OK):**
```json
{
  "message": "Last reconciliation data retrieved successfully",
  "data": {
    "previous_session_id": 15,
    "previous_session_end_time": "2023-01-01T18:00:00.000Z",
    "previous_session_user": {
      "id": 3,
      "name": "John Doe"
    },
    "closing_cash_balance": 15000.00,
    "closing_mpesa_balance": 8000.00,
    "closing_mpesa_float": 12000.00
  }
}
```

---

## Error Responses

All endpoints may return the following error responses:

**401 Unauthorized:**
```json
{
  "error": "Unauthorized",
  "message": "Invalid or missing authentication token"
}
```

**403 Forbidden:**
```json
{
  "error": "Forbidden",
  "message": "Insufficient permissions"
}
```

**404 Not Found:**
```json
{
  "error": "Not Found",
  "message": "Resource not found"
}
```

**400 Bad Request:**
```json
{
  "error": "Bad Request",
  "message": "Invalid input data",
  "details": ["Field 'amount' is required"]
}
```

**500 Internal Server Error:**
```json
{
  "error": "Internal Server Error",
  "message": "An unexpected error occurred"
}
```

---

## Notes

1. **Pagination**: Most list endpoints support pagination with `page` and `limit` parameters
2. **Date Formats**: All dates should be in YYYY-MM-DD format for query parameters
3. **Float Values**: All monetary amounts are returned as decimal numbers with 2 decimal places
4. **Status Values**:
   - Movement statuses: pending, in_transit, received, cancelled
   - Top-up statuses: pending, completed, cancelled
5. **HQ Operations**: Use `branch_id: null` or `branchId: 'hq'` for headquarters operations
6. **Authentication**: All endpoints require valid JWT token in Authorization header
7. **Permissions**: Different endpoints require different RBAC permissions as specified in the route definitions
8. **Float Reconciliation**: The system automatically calculates discrepancies and updates branch balances
9. **Reference Numbers**: Top-ups automatically generate unique reference numbers in format "MPESA-TOPUP-{timestamp}-{random}"
10. **Transaction Integrity**: All float operations use database transactions to ensure data consistency