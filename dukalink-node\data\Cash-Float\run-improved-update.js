#!/usr/bin/env node

/**
 * IMPROVED UPDATE RUNNER - MAKES ACTUAL DATABASE CHANGES
 * 
 * This script runs the improved update that fixes issues found in the original script:
 * 1. Includes Voi branch (was wrongly excluded)
 * 2. Corrects data discrepancies found between Excel and hardcoded data
 * 3. Provides comprehensive coverage of all branches
 * 
 * ⚠️ WARNING: THIS SCRIPT MAKES ACTUAL DATABASE CHANGES
 * 
 * Run with: node data/cash-float/run-improved-update.js
 */

const path = require('path');
const readline = require('readline');

// Set up environment
require('dotenv').config({ path: path.join(__dirname, '../../.env') });

// Import the improved update function
const { improvedUpdateClosingBalances } = require('./improved-update-closing-balances');

// Create readline interface for user confirmation
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer);
    });
  });
}

async function main() {
  console.log('🔄 IMPROVED UPDATE MODE - ACTUAL DATABASE CHANGES');
  console.log('==================================================');
  console.log('⚠️  WARNING: THIS WILL MODIFY THE DATABASE!');
  console.log('🔧 IMPROVEMENTS IN THIS VERSION:');
  console.log('   ✅ Includes Voi branch (was wrongly excluded)');
  console.log('   ✅ Corrects Mariakani 2 values (19,750 cash, 4,748 float)');
  console.log('   ✅ Corrects Prudential values (136,521 cash, 17,602 float)');
  console.log('   ✅ Fixes branch name mapping issues');
  console.log('   ✅ Comprehensive coverage of all Excel branches');
  console.log('==================================================');
  
  console.log('\n📋 EXPECTED CHANGES (from preview):');
  console.log('   • Bungoma: Fix extreme values');
  console.log('   • Homabay: Cash +5,939, Float -21,320');
  console.log('   • Karatina: Cash +554, Float -24,240');
  console.log('   • Magunga: Cash +2,484, Float -15,020');
  console.log('   • Malaba: Fix extreme values');
  console.log('   • Mariakani 2: Swap values (4,748 ↔ 19,750)');
  console.log('   • Mombasa: Fix extreme values');
  console.log('   • Prudential: Cash +81,795, Float +7,500');
  console.log('   • Voi: Cash -22,574, Float +177,145');
  console.log('   • Plus other branches as needed');
  
  console.log('\n🎯 TOTAL EXPECTED UPDATES: ~10 reconciliations');
  console.log('📊 BRANCHES ALREADY CORRECT: ~58 reconciliations');
  
  const answer = await askQuestion('\n❓ Do you want to proceed with the actual database update? (yes/no): ');
  
  if (answer.toLowerCase() !== 'yes' && answer.toLowerCase() !== 'y') {
    console.log('\n❌ Update cancelled by user.');
    rl.close();
    process.exit(0);
  }
  
  console.log('\n🚀 Starting improved update process...');
  console.log('⏳ This may take a few moments...\n');
  
  try {
    const results = await improvedUpdateClosingBalances();
    
    console.log('\n✅ Improved update completed successfully!');
    console.log('📊 FINAL SUMMARY:');
    console.log(`   • Excel branches processed: ${results.excelBranches}`);
    console.log(`   • Branches found in DB: ${results.foundInDB}`);
    console.log(`   • Reconciliations updated: ${results.updatedValues}`);
    console.log(`   • Already correct (skipped): ${results.correctValues}`);
    console.log(`   • Errors encountered: ${results.errors.length}`);
    
    if (results.updatedValues > 0) {
      console.log('\n🎉 SUCCESS! The following improvements were applied:');
      console.log('   ✅ Voi branch balances updated (was previously excluded)');
      console.log('   ✅ Mariakani 2 values corrected (swapped values fixed)');
      console.log('   ✅ Prudential values corrected (higher amounts from Excel)');
      console.log('   ✅ All other discrepancies resolved');
    }
    
    if (results.errors.length > 0) {
      console.log('\n⚠️ Some issues were encountered:');
      results.errors.forEach(error => {
        console.log(`   • ${error}`);
      });
    }
    
    console.log('\n💡 All 27th May closing balances are now accurate and complete!');
    console.log('🔍 You can verify the changes by running the preview script again.');
    
  } catch (error) {
    console.error('\n💥 Update failed:', error.message);
    console.error('🔄 All changes have been rolled back.');
    console.error('📋 Database remains unchanged.');
    
    if (error.stack) {
      console.error('\n📝 Error details:');
      console.error(error.stack);
    }
    
    rl.close();
    process.exit(1);
  }
  
  rl.close();
  process.exit(0);
}

// Run the main function
main().catch((error) => {
  console.error('💥 Fatal error:', error);
  rl.close();
  process.exit(1);
});
