const path = require("path");
const {
  Branch,
  PosSession,
  PosSessionReconciliation,
  User,
} = require("../../src/models");
const sequelize = require("../../config/database");
const logger = require("../../src/utils/logger");

/**
 * ENHANCED PREVIEW SCRIPT - NO DATABASE CHANGES
 * 
 * This script simulates:
 * 1. Updating closing balances for existing POS sessions from 27th May
 * 2. Creating missing POS sessions for branches without sessions on 27th May
 * 3. Creating reconciliation records for all sessions
 * 
 * Shows exactly what would be changed/created without making any database modifications
 */

// Excel data from 27th balances.xlsx with corrected branch names
const balancesData = [
  { branch: "Bungoma", cash: 13449, float: 10257 },
  { branch: "Bus<PERSON>", cash: 17363, float: 14519 },
  { branch: "Chu<PERSON>", cash: 21801, float: 1928 },
  { branch: "<PERSON><PERSON>", cash: 16150, float: 55958 },
  { branch: "Ho<PERSON><PERSON>", cash: 70312, float: 4445 },
  { branch: "Kapenguria", cash: 13325, float: 4161 },
  { branch: "Kapsabe<PERSON>", cash: 5270, float: 13395 },
  { branch: "Karatina", cash: 5425, float: 2073 },
  { branch: "<PERSON>honzweni", cash: 29304, float: 935 },
  { branch: "Kehancha", cash: 750, float: 20832 },
  { branch: "Kericho", cash: 20040, float: 3709 },
  { branch: "Kerugoya", cash: 23390, float: 3792 },
  { branch: "Kibwezi", cash: 16430, float: 17145 },
  { branch: "Kikima", cash: 7985, float: 80133 },
  { branch: "Kilgoris", cash: 30923, float: 11159 },
  { branch: "Kilifi", cash: 7275, float: 26675 },
  { branch: "Kisumu", cash: 72153, float: 43634 },
  { branch: "Kitale 2", cash: 24014, float: 14414 },
  { branch: "Kitui", cash: 21014, float: 2016 },
  { branch: "Likoni", cash: 34535, float: 17243 },
  { branch: "Machakos", cash: 28009, float: 13725 },
  { branch: "Magunga", cash: 9805, float: 26438 },
  { branch: "Malaba", cash: 21463, float: 24168 },
  { branch: "Mariakani 2", cash: 4748, float: 19750 },
  { branch: "Mau summit", cash: 34440, float: 9560 },
  { branch: "Maua", cash: 14920, float: 16424 },
  { branch: "Mbale", cash: 19145, float: 68177 },
  { branch: "Mbita", cash: 28038, float: 624 },
  { branch: "Mombasa ", cash: 34821, float: 96381 },
  { branch: "Mtwapa", cash: 5696, float: 54296 },
  { branch: "Muhuru Bay", cash: 18140, float: 15260 },
  { branch: "Mumias", cash: 3572, float: 11060 },
  { branch: "Mutomo", cash: 10365, float: 29463 },
  { branch: "Mwatate", cash: 12668, float: 9057 },
  { branch: "Mwingi", cash: 4304, float: 15261 },
  { branch: "Nkubu", cash: 18900, float: 656 },
  { branch: "Nyamira", cash: 40006, float: 1896 },
  { branch: "Nyeri", cash: 28769, float: 20628 },
  { branch: "Oyugis", cash: 12518, float: 13794 },
  { branch: "Prudential", cash: 54726, float: 10102 },
  { branch: "Sori", cash: 32, float: 2599 },
  { branch: "Webuye", cash: 39962, float: 17681 },
  { branch: "Wote", cash: 16041, float: 5890 },
  { branch: "Wundanyi", cash: 9830, float: 12458 }
];

async function previewWithSessionCreation() {
  try {
    logger.info('🔍 ENHANCED PREVIEW MODE - NO DATABASE CHANGES WILL BE MADE');
    logger.info('==========================================================');
    logger.info('📅 Analyzing POS sessions and reconciliations for 27th May 2025...');
    
    const previewResults = [];
    const sessionCreations = [];
    const errors = [];
    const warnings = [];
    
    // Step 1: Find a system user for creating sessions
    logger.info('\n📋 Step 1: Finding system user for session creation...');
    
    const systemUser = await User.findOne({
      where: {
        role_id: 11, // company_admin
        deleted_at: null
      }
    });
    
    if (!systemUser) {
      errors.push('No company_admin user found for creating system sessions');
      logger.error('❌ No company_admin user found');
    } else {
      logger.info(`✅ Found system user: ${systemUser.name} (ID: ${systemUser.id})`);
    }
    
    // Step 2: Validate all branches exist
    logger.info('\n📋 Step 2: Validating branch names from Excel data...');
    const branchValidation = [];
    
    for (const balanceEntry of balancesData) {
      const { branch: branchName } = balanceEntry;
      
      logger.info(`🔍 Checking branch: "${branchName}"`);
      
      const branch = await Branch.findOne({
        where: { 
          name: branchName,
          deleted_at: null 
        }
      });
      
      if (branch) {
        branchValidation.push({
          branchName,
          branchId: branch.id,
          status: 'FOUND',
          location: branch.location
        });
        logger.info(`  ✅ Found: ID ${branch.id}, Location: ${branch.location}`);
      } else {
        branchValidation.push({
          branchName,
          branchId: null,
          status: 'NOT_FOUND'
        });
        errors.push(`Branch not found: "${branchName}"`);
        logger.error(`  ❌ Not found: "${branchName}"`);
      }
    }
    
    // Step 3: Find all POS sessions from 27th May 2025
    logger.info('\n📋 Step 3: Finding existing POS sessions from 27th May 2025...');
    
    const targetDate = '2025-05-27';
    const startOfDay = `${targetDate} 00:00:00`;
    const endOfDay = `${targetDate} 23:59:59`;
    
    logger.info(`🗓️ Searching for sessions between ${startOfDay} and ${endOfDay}`);
    
    const { Op } = require('sequelize');
    
    const may27Sessions = await PosSession.findAll({
      where: {
        start_time: {
          [Op.between]: [startOfDay, endOfDay]
        },
        deleted_at: null
      },
      include: [
        {
          model: Branch,
          attributes: ['id', 'name', 'location']
        },
        {
          model: PosSessionReconciliation,
          as: 'reconciliation',
          required: false
        }
      ],
      order: [['start_time', 'ASC']]
    });
    
    logger.info(`📊 Found ${may27Sessions.length} existing POS sessions from 27th May 2025`);
    
    // Step 4: Process each balance entry
    logger.info('\n📋 Step 4: Analyzing required changes and session creations...');
    
    for (const balanceEntry of balancesData) {
      const { branch: branchName, cash: newCashBalance, float: newFloatBalance } = balanceEntry;
      
      logger.info(`\n🏪 Processing: ${branchName}`);
      logger.info(`   💰 Target Cash: ${newCashBalance.toLocaleString()}`);
      logger.info(`   📱 Target Float: ${newFloatBalance.toLocaleString()}`);
      
      // Find branch
      const branchInfo = branchValidation.find(b => b.branchName === branchName);
      if (!branchInfo || branchInfo.status !== 'FOUND') {
        logger.error(`   ❌ Skipping - Branch not found`);
        continue;
      }
      
      // Find sessions for this branch on 27th May
      const branchSessions = may27Sessions.filter(session => 
        session.Branch && session.Branch.name === branchName
      );
      
      if (branchSessions.length === 0) {
        // No sessions found - need to create one
        logger.warn(`   ⚠️ No sessions found - WOULD CREATE NEW SESSION`);
        
        if (systemUser) {
          const sessionStartTime = new Date(`${targetDate}T08:00:00.000Z`);
          const sessionEndTime = new Date(`${targetDate}T20:00:00.000Z`);
          
          logger.info(`   ➕ WOULD CREATE POS SESSION:`);
          logger.info(`      👤 User: ${systemUser.name} (ID: ${systemUser.id})`);
          logger.info(`      🏢 Branch: ${branchName} (ID: ${branchInfo.branchId})`);
          logger.info(`      ⏰ Start: ${sessionStartTime.toISOString()}`);
          logger.info(`      🔚 End: ${sessionEndTime.toISOString()}`);
          logger.info(`      💰 Opening Cash: 1000.00 (default)`);
          logger.info(`      📱 Opening Float: 1000.00 (default)`);
          logger.info(`      📊 Status: closed`);
          
          logger.info(`   ➕ WOULD CREATE RECONCILIATION:`);
          logger.info(`      💰 Closing Cash: ${newCashBalance}`);
          logger.info(`      📱 Closing Float: ${newFloatBalance}`);
          
          sessionCreations.push({
            action: 'CREATE_SESSION_AND_RECONCILIATION',
            branchName,
            branchId: branchInfo.branchId,
            userId: systemUser.id,
            userName: systemUser.name,
            sessionStartTime,
            sessionEndTime,
            openingCash: 1000.00,
            openingFloat: 1000.00,
            closingCash: newCashBalance,
            closingFloat: newFloatBalance,
            status: 'closed'
          });
        } else {
          warnings.push(`Cannot create session for ${branchName} - no system user available`);
        }
        
        continue;
      }
      
      logger.info(`   📋 Found ${branchSessions.length} existing session(s):`);
      
      // Process each existing session
      for (const session of branchSessions) {
        logger.info(`\n   🔍 Session ID: ${session.id}`);
        logger.info(`      ⏰ Start Time: ${session.start_time}`);
        logger.info(`      📊 Status: ${session.status}`);
        
        const existingReconciliation = session.reconciliation;
        
        if (existingReconciliation) {
          logger.info(`      📝 Existing Reconciliation ID: ${existingReconciliation.id}`);
          logger.info(`         💰 Current Cash: ${existingReconciliation.closing_cash_balance || 'NULL'}`);
          logger.info(`         📱 Current Float: ${existingReconciliation.closing_mpesa_float || 'NULL'}`);
          logger.info(`      🔄 WOULD UPDATE RECONCILIATION:`);
          logger.info(`         💰 Cash: ${existingReconciliation.closing_cash_balance || 'NULL'} → ${newCashBalance}`);
          logger.info(`         📱 Float: ${existingReconciliation.closing_mpesa_float || 'NULL'} → ${newFloatBalance}`);
          
          previewResults.push({
            action: 'UPDATE_RECONCILIATION',
            branchName,
            sessionId: session.id,
            reconciliationId: existingReconciliation.id,
            currentCash: existingReconciliation.closing_cash_balance,
            newCash: newCashBalance,
            currentFloat: existingReconciliation.closing_mpesa_float,
            newFloat: newFloatBalance,
            sessionStartTime: session.start_time,
            sessionStatus: session.status
          });
        } else {
          logger.info(`      📝 No existing reconciliation`);
          logger.info(`      ➕ WOULD CREATE NEW RECONCILIATION:`);
          logger.info(`         💰 Cash: ${newCashBalance}`);
          logger.info(`         📱 Float: ${newFloatBalance}`);
          
          previewResults.push({
            action: 'CREATE_RECONCILIATION',
            branchName,
            sessionId: session.id,
            reconciliationId: null,
            currentCash: null,
            newCash: newCashBalance,
            currentFloat: null,
            newFloat: newFloatBalance,
            sessionStartTime: session.start_time,
            sessionStatus: session.status
          });
        }
      }
    }
    
    // Summary Report
    logger.info('\n📊 ENHANCED PREVIEW SUMMARY REPORT');
    logger.info('===================================');
    logger.info(`✅ Branches validated: ${branchValidation.filter(b => b.status === 'FOUND').length}/${branchValidation.length}`);
    logger.info(`📅 Existing 27th May sessions: ${may27Sessions.length}`);
    logger.info(`🔄 Reconciliations to UPDATE: ${previewResults.filter(r => r.action === 'UPDATE_RECONCILIATION').length}`);
    logger.info(`➕ Reconciliations to CREATE: ${previewResults.filter(r => r.action === 'CREATE_RECONCILIATION').length}`);
    logger.info(`🏗️ POS Sessions to CREATE: ${sessionCreations.length}`);
    logger.info(`⚠️ Warnings: ${warnings.length}`);
    logger.info(`❌ Errors: ${errors.length}`);
    
    if (sessionCreations.length > 0) {
      logger.info('\n🏗️ NEW SESSIONS TO CREATE:');
      sessionCreations.forEach((creation, index) => {
        logger.info(`\n${index + 1}. ${creation.branchName}`);
        logger.info(`   👤 User: ${creation.userName} (${creation.userId})`);
        logger.info(`   ⏰ Time: ${creation.sessionStartTime.toISOString()} - ${creation.sessionEndTime.toISOString()}`);
        logger.info(`   💰 Opening: Cash ${creation.openingCash}, Float ${creation.openingFloat}`);
        logger.info(`   💰 Closing: Cash ${creation.closingCash}, Float ${creation.closingFloat}`);
      });
    }
    
    if (warnings.length > 0) {
      logger.info('\n⚠️ WARNINGS:');
      warnings.forEach(warning => logger.warn(`  • ${warning}`));
    }
    
    if (errors.length > 0) {
      logger.info('\n❌ ERRORS:');
      errors.forEach(error => logger.error(`  • ${error}`));
    }
    
    logger.info('\n🎉 Enhanced preview completed successfully!');
    logger.info('💡 Review all changes above before running the actual update script.');
    
    return {
      previewResults,
      sessionCreations,
      errors,
      warnings,
      branchValidation,
      totalExistingSessions: may27Sessions.length,
      systemUser
    };
    
  } catch (error) {
    logger.error('💥 Fatal error during enhanced preview:', error);
    throw error;
  }
}

// Execute the preview
if (require.main === module) {
  previewWithSessionCreation()
    .then((results) => {
      logger.info('✅ Enhanced preview execution completed');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('💥 Enhanced preview execution failed:', error);
      process.exit(1);
    });
}

module.exports = { previewWithSessionCreation };
