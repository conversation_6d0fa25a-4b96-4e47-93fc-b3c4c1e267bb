const path = require("path");
const {
  Branch,
  PosSession,
  PosSessionReconciliation,
} = require("../../src/models");
const sequelize = require("../../config/database");
const logger = require("../../src/utils/logger");

/**
 * COMPREHENSIVE DUPLICATE RECONCILIATION CLEANUP
 * 
 * This script cleans up duplicate reconciliations found in the comprehensive check:
 * - Homabay, Malaba, Webuye, Bungoma, Karatina, Magunga (6 branches)
 * - Keeps reconciliations that match Excel values
 * - Removes reconciliations created by mobile app with wrong values
 * 
 * MODES:
 * - PREVIEW: Shows what would be done (NO DATABASE CHANGES)
 * - EXECUTE: Actually performs the cleanup (MODIFIES DATABASE)
 */

// Expected Excel values for validation
const expectedBalances = {
  "Homabay": { cash: 70312, float: 4445 },
  "Malaba": { cash: 21463, float: 24168 },
  "Webuye": { cash: 39962, float: 17681 },
  "Bungoma": { cash: 13449, float: 10257 },
  "Karatina": { cash: 5425, float: 2073 },
  "Magunga": { cash: 9805, float: 26438 }
};

// Sessions with known duplicate reconciliation issues
const problematicSessions = [
  { sessionId: 149, branchName: "Homabay" },
  { sessionId: 133, branchName: "Malaba" },
  { sessionId: 162, branchName: "Webuye" },
  { sessionId: 131, branchName: "Bungoma" },
  { sessionId: 110, branchName: "Karatina" },
  { sessionId: 157, branchName: "Magunga" }
];

async function comprehensiveCleanupDuplicates(mode = 'PREVIEW') {
  const isPreview = mode === 'PREVIEW';
  const transaction = isPreview ? null : await sequelize.transaction();
  
  try {
    logger.info(`🔧 COMPREHENSIVE DUPLICATE CLEANUP - ${mode} MODE`);
    logger.info('=====================================================');
    
    if (isPreview) {
      logger.info('⚠️ PREVIEW MODE - NO DATABASE CHANGES WILL BE MADE');
      logger.info('📋 This will show what would be cleaned up');
    } else {
      logger.info('🚨 EXECUTE MODE - ACTUAL DATABASE CHANGES WILL BE MADE');
      logger.info('⚠️ WARNING: This will modify the production database!');
    }
    
    logger.info('🎯 Target: 6 branches with duplicate reconciliations');
    logger.info('✅ Strategy: Keep Excel-matching reconciliations, remove mobile app duplicates');
    
    const results = {
      sessionsAnalyzed: 0,
      duplicatesFound: 0,
      correctReconciliations: 0,
      incorrectReconciliations: 0,
      reconciliationsToDelete: [],
      reconciliationsToKeep: [],
      errors: [],
      summary: {}
    };
    
    // Step 1: Analyze each problematic session
    logger.info('\n📋 Step 1: Analyzing problematic sessions...');
    
    for (const { sessionId, branchName } of problematicSessions) {
      logger.info(`\n🏪 Processing: ${branchName} (Session ${sessionId})`);
      
      const expectedData = expectedBalances[branchName];
      if (!expectedData) {
        results.errors.push(`No expected data for ${branchName}`);
        logger.error(`   ❌ No expected data found`);
        continue;
      }
      
      logger.info(`   📋 Expected: Cash ${expectedData.cash.toLocaleString()}, Float ${expectedData.float.toLocaleString()}`);
      
      // Get all reconciliations for this session
      const reconciliations = await PosSessionReconciliation.findAll({
        where: { pos_session_id: sessionId },
        order: [['created_at', 'ASC']],
        ...(transaction && { transaction })
      });
      
      results.sessionsAnalyzed++;
      
      if (reconciliations.length <= 1) {
        logger.info(`   ✅ Only ${reconciliations.length} reconciliation(s) - no cleanup needed`);
        continue;
      }
      
      results.duplicatesFound++;
      logger.warn(`   ⚠️ Found ${reconciliations.length} reconciliations - cleanup needed`);
      
      // Analyze each reconciliation
      let correctReconciliation = null;
      const incorrectReconciliations = [];
      
      reconciliations.forEach((rec, index) => {
        const cash = parseFloat(rec.closing_cash_balance || 0);
        const float = parseFloat(rec.closing_mpesa_float || 0);
        
        logger.info(`      ${index + 1}. ID: ${rec.id}, Cash: ${cash.toLocaleString()}, Float: ${float.toLocaleString()}`);
        logger.info(`         Notes: ${rec.notes || 'No notes'}`);
        logger.info(`         Created: ${rec.created_at}`);
        
        // Check if this matches Excel values
        const cashMatches = Math.abs(cash - expectedData.cash) < 0.01;
        const floatMatches = Math.abs(float - expectedData.float) < 0.01;
        
        if (cashMatches && floatMatches) {
          if (correctReconciliation) {
            logger.warn(`         ⚠️ Multiple correct reconciliations found!`);
            // Keep the most recent correct one
            if (rec.created_at > correctReconciliation.created_at) {
              incorrectReconciliations.push(correctReconciliation);
              correctReconciliation = rec;
              logger.info(`         ✅ Matches Excel - KEEPING (newer)`);
            } else {
              incorrectReconciliations.push(rec);
              logger.info(`         ✅ Matches Excel but older - REMOVING`);
            }
          } else {
            correctReconciliation = rec;
            logger.info(`         ✅ Matches Excel - KEEPING`);
          }
        } else {
          incorrectReconciliations.push(rec);
          logger.warn(`         ❌ Wrong values - REMOVING`);
          logger.warn(`            Expected: Cash ${expectedData.cash.toLocaleString()}, Float ${expectedData.float.toLocaleString()}`);
        }
      });
      
      // Validate we found exactly one correct reconciliation
      if (!correctReconciliation) {
        results.errors.push(`${branchName}: No reconciliation matches Excel values`);
        logger.error(`   ❌ ERROR: No reconciliation matches Excel values!`);
        continue;
      }
      
      if (incorrectReconciliations.length === 0) {
        logger.info(`   ✅ All reconciliations are correct - no cleanup needed`);
        continue;
      }
      
      // Record the actions
      results.correctReconciliations++;
      results.incorrectReconciliations += incorrectReconciliations.length;
      
      results.reconciliationsToKeep.push({
        branchName,
        sessionId,
        reconciliationId: correctReconciliation.id,
        cash: parseFloat(correctReconciliation.closing_cash_balance),
        float: parseFloat(correctReconciliation.closing_mpesa_float),
        notes: correctReconciliation.notes
      });
      
      incorrectReconciliations.forEach(rec => {
        results.reconciliationsToDelete.push({
          branchName,
          sessionId,
          reconciliationId: rec.id,
          cash: parseFloat(rec.closing_cash_balance),
          float: parseFloat(rec.closing_mpesa_float),
          notes: rec.notes,
          created_at: rec.created_at
        });
      });
      
      logger.info(`   📊 Summary: Keeping 1, Removing ${incorrectReconciliations.length}`);
      
      // Execute deletion if not in preview mode
      if (!isPreview) {
        logger.info(`   🗑️ Deleting ${incorrectReconciliations.length} incorrect reconciliation(s)...`);
        
        for (const incorrectRec of incorrectReconciliations) {
          logger.info(`      🗑️ Deleting reconciliation ${incorrectRec.id}...`);
          await incorrectRec.destroy({ transaction });
          logger.info(`      ✅ Successfully deleted reconciliation ${incorrectRec.id}`);
        }
      } else {
        logger.info(`   👁️ PREVIEW: Would delete ${incorrectReconciliations.length} reconciliation(s)`);
      }
    }
    
    // Step 2: Summary and validation
    logger.info('\n📊 COMPREHENSIVE CLEANUP SUMMARY');
    logger.info('=================================');
    logger.info(`📋 Sessions analyzed: ${results.sessionsAnalyzed}`);
    logger.info(`🔄 Sessions with duplicates: ${results.duplicatesFound}`);
    logger.info(`✅ Correct reconciliations to keep: ${results.correctReconciliations}`);
    logger.info(`❌ Incorrect reconciliations to remove: ${results.incorrectReconciliations}`);
    logger.info(`⚠️ Errors encountered: ${results.errors.length}`);
    
    // Detailed breakdown
    if (results.reconciliationsToKeep.length > 0) {
      logger.info('\n✅ RECONCILIATIONS TO KEEP:');
      results.reconciliationsToKeep.forEach(item => {
        logger.info(`   • ${item.branchName} (Session ${item.sessionId}, Rec ${item.reconciliationId})`);
        logger.info(`     Cash: ${item.cash.toLocaleString()}, Float: ${item.float.toLocaleString()}`);
      });
    }
    
    if (results.reconciliationsToDelete.length > 0) {
      logger.info(`\n${isPreview ? '👁️ RECONCILIATIONS TO DELETE (PREVIEW):' : '🗑️ RECONCILIATIONS DELETED:'}`);
      results.reconciliationsToDelete.forEach(item => {
        logger.info(`   • ${item.branchName} (Session ${item.sessionId}, Rec ${item.reconciliationId})`);
        logger.info(`     Cash: ${item.cash.toLocaleString()}, Float: ${item.float.toLocaleString()}`);
        logger.info(`     Created: ${item.created_at}`);
      });
    }
    
    if (results.errors.length > 0) {
      logger.info('\n❌ ERRORS ENCOUNTERED:');
      results.errors.forEach(error => {
        logger.error(`   • ${error}`);
      });
    }
    
    // Commit or rollback
    if (!isPreview) {
      if (results.errors.length > 0) {
        await transaction.rollback();
        logger.error('\n💥 Errors encountered - transaction rolled back');
        throw new Error('Cleanup failed due to errors');
      } else {
        await transaction.commit();
        logger.info('\n✅ All changes committed successfully');
      }
    }
    
    // Final recommendations
    logger.info('\n💡 RECOMMENDATIONS:');
    if (isPreview) {
      if (results.reconciliationsToDelete.length > 0) {
        logger.info('   🔧 Run in EXECUTE mode to perform the cleanup');
        logger.info('   📋 Review the reconciliations to be deleted above');
        logger.info('   ⚠️ Ensure you have database backups before executing');
      } else {
        logger.info('   ✅ No cleanup needed - all data is already clean');
      }
    } else {
      logger.info('   ✅ Cleanup completed successfully');
      logger.info('   📋 Run comprehensive discrepancy check again to verify');
      logger.info('   🚀 Proceed with final balance updates when ready');
    }
    
    const totalChanges = results.reconciliationsToDelete.length;
    logger.info(`\n📊 TOTAL ${isPreview ? 'PLANNED' : 'COMPLETED'} CHANGES: ${totalChanges}`);
    
    if (!isPreview && totalChanges > 0) {
      logger.info('\n🎉 Comprehensive cleanup completed successfully!');
      logger.info('💡 All duplicate reconciliations have been resolved.');
      logger.info('🔍 Each session now has exactly one reconciliation with correct Excel values.');
    }
    
    return results;
    
  } catch (error) {
    if (!isPreview && transaction) {
      await transaction.rollback();
    }
    logger.error('💥 Error during comprehensive cleanup:', error);
    throw error;
  }
}

// Execute the cleanup
if (require.main === module) {
  const mode = process.argv[2] || 'PREVIEW';
  
  if (!['PREVIEW', 'EXECUTE'].includes(mode)) {
    console.error('Usage: node comprehensive-cleanup-duplicates.js [PREVIEW|EXECUTE]');
    process.exit(1);
  }
  
  comprehensiveCleanupDuplicates(mode)
    .then((results) => {
      logger.info(`✅ Comprehensive cleanup ${mode.toLowerCase()} completed`);
      process.exit(0);
    })
    .catch((error) => {
      logger.error(`💥 Comprehensive cleanup ${mode.toLowerCase()} failed:`, error);
      process.exit(1);
    });
}

module.exports = { comprehensiveCleanupDuplicates };
