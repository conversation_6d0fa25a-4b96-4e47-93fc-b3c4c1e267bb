# DSA Pricing Analysis and Fixes

## Current Issues Identified

### 1. Database Analysis Results

**DSA Sales Pricing Inconsistency:**
- 33 sale items using **RETAIL prices** (total value: KSh 207,900.00)
- 47 sale items using **WHOLESALE prices** (total value: KSh 252,600.00)

**Sample Data:**
```sql
-- DSA Sales with RETAIL pricing (INCORRECT)
Sale ID 1113: ATC 00010 - Unit Price: 10.00 (Retail) vs 9.50 (Wholesale)
Sale ID 1031: ATC 00050 - Unit Price: 50.00 (Retail) vs 47.50 (Wholesale)

-- DSA Sales with WHOLESALE pricing (CORRECT)
Sale ID 1112: ATC 00010 - Unit Price: 9.50 (Wholesale) ✓
Sale ID 1030: ATC 00050 - Unit Price: 47.50 (Wholesale) ✓
```

### 2. Code Issues

#### A. PriceCalculator Hardcoded for Retail
**File:** `src/utils/price-calculator.js` (Lines 31-37)
```javascript
// PROBLEM: Always prioritizes retail pricing
// ALWAYS prioritize retail pricing (default_selling_price) for mobile sales
if (product.default_selling_price) {
  basePrice = parseFloat(product.default_selling_price);
  console.log(`Using retail price (default_selling_price): ${basePrice}`);
}
```

#### B. DSA Sales Controller Uses PriceCalculator
**File:** `src/controllers/sale.controller.js` (Lines 2204-2213)
```javascript
// PROBLEM: DSA sales use PriceCalculator which defaults to retail
const priceDetails = await PriceCalculator.calculatePrice(
  productWithPricing,
  quantity_sold,
  null, // customer
  {
    date: new Date(),
    branchId: dsaUser.branch_id,
    regionId: regionId,
  }
);
```

#### C. Inconsistent Implementation
- **DSA Stock Assignments** correctly use wholesale pricing
- **DSA Sales** incorrectly use retail pricing through PriceCalculator
- **DSA Batch Operations** correctly use wholesale pricing

## Required Fixes

### 1. Create DSA-Specific Price Calculator

Create a new utility specifically for DSA operations that prioritizes wholesale pricing:

**File:** `src/utils/dsa-price-calculator.js`
```javascript
class DsaPriceCalculator {
  static async calculateDsaPrice(product, quantity = 1, options = {}) {
    // ALWAYS prioritize wholesale pricing for DSA operations
    let basePrice = 0;
    
    // 1. Check wholesale price first
    if (product.default_wholesale_price && product.default_wholesale_price > 0) {
      basePrice = parseFloat(product.default_wholesale_price);
    }
    // 2. Fallback to selling price if wholesale not set
    else if (product.default_selling_price) {
      basePrice = parseFloat(product.default_selling_price);
    }
    // 3. Check StockItem wholesale price
    else if (product.StockItem && product.StockItem.default_wholesale_price > 0) {
      basePrice = parseFloat(product.StockItem.default_wholesale_price);
    }
    // 4. Fallback to StockItem selling price
    else if (product.StockItem && product.StockItem.default_selling_price) {
      basePrice = parseFloat(product.StockItem.default_selling_price);
    }
    
    // DSA operations don't typically use discounts - use wholesale price as-is
    return {
      original_price: basePrice,
      final_price: basePrice,
      discount_amount: 0,
      discount_percentage: 0,
      discount_type: null,
      discount_name: null,
      is_wholesale: true,
      quantity: quantity,
      total_price: basePrice * quantity,
      total_discount: 0
    };
  }
}
```

### 2. Update DSA Sales Controller

**File:** `src/controllers/sale.controller.js`

Replace the PriceCalculator usage in `createDsaSale` function:

```javascript
// BEFORE (Lines 2204-2213)
const priceDetails = await PriceCalculator.calculatePrice(
  productWithPricing,
  quantity_sold,
  null,
  { date: new Date(), branchId: dsaUser.branch_id, regionId: regionId }
);

// AFTER
const DsaPriceCalculator = require("../utils/dsa-price-calculator");
const priceDetails = await DsaPriceCalculator.calculateDsaPrice(
  productWithPricing,
  quantity_sold,
  { date: new Date(), branchId: dsaUser.branch_id, regionId: regionId }
);
```

### 3. Add DSA Price Validation

Add validation to ensure DSA sales always use wholesale pricing:

```javascript
// In createDsaSale function, after price calculation
const expectedWholesalePrice = stockItem.default_wholesale_price > 0 
  ? parseFloat(stockItem.default_wholesale_price)
  : parseFloat(stockItem.default_selling_price);

if (Math.abs(priceDetails.final_price - expectedWholesalePrice) > 0.01) {
  logger.warn(`DSA sale price mismatch: Expected ${expectedWholesalePrice}, got ${priceDetails.final_price}`);
}
```

### 4. Database Cleanup Script

Create a script to identify and optionally fix existing DSA sales with incorrect pricing:

```sql
-- Identify DSA sales using retail instead of wholesale pricing
SELECT 
    s.id as sale_id,
    si.product_id,
    si.unit_price as current_price,
    st.default_wholesale_price as correct_wholesale_price,
    st.default_selling_price as retail_price,
    (si.unit_price - st.default_wholesale_price) as price_difference,
    si.quantity,
    (si.unit_price - st.default_wholesale_price) * si.quantity as total_overcharge
FROM sales s
JOIN sale_items si ON s.id = si.sale_id
JOIN stock_items st ON si.product_id = st.product_id AND s.branch_id = st.branch_id
WHERE (s.is_dsa = 1 OR s.sale_type = 'dsa')
AND st.default_wholesale_price > 0
AND si.unit_price = st.default_selling_price
AND si.unit_price != st.default_wholesale_price
ORDER BY total_overcharge DESC;
```

### 5. Add DSA Sale Type Enforcement

Update the sales table to properly track DSA sales:

```sql
-- Ensure all DSA sales are properly marked
UPDATE sales 
SET sale_type = 'dsa' 
WHERE is_dsa = 1 AND (sale_type IS NULL OR sale_type = 'normal');
```

## Implementation Priority

### Phase 1: Immediate Fixes (High Priority)
1. ✅ Create `DsaPriceCalculator` utility
2. ✅ Update `createDsaSale` controller to use wholesale pricing
3. ✅ Add price validation for DSA operations

### Phase 2: Data Cleanup (Medium Priority)
1. ✅ Run analysis query to identify incorrect pricing
2. ✅ Create correction script for existing data
3. ✅ Update sale_type for proper tracking

### Phase 3: Prevention (Low Priority)
1. ✅ Add unit tests for DSA pricing
2. ✅ Add monitoring/alerts for pricing discrepancies
3. ✅ Update API documentation

## Expected Impact

### Financial Impact
- **Current Loss**: Approximately KSh 207,900 in sales at retail instead of wholesale
- **Per-item Impact**: Average 5-10% price difference (e.g., 10.00 vs 9.50)
- **Future Prevention**: Ensures all DSA operations use correct wholesale pricing

### Operational Impact
- **Consistency**: All DSA operations will use wholesale pricing
- **Transparency**: Clear separation between retail and wholesale sales
- **Reporting**: Accurate profit margin calculations for DSA operations

## Testing Strategy

### 1. Unit Tests
```javascript
describe('DSA Pricing', () => {
  it('should use wholesale price for DSA sales', async () => {
    const product = {
      id: 304,
      default_selling_price: 10.00,
      default_wholesale_price: 9.50
    };
    
    const priceDetails = await DsaPriceCalculator.calculateDsaPrice(product, 1);
    expect(priceDetails.final_price).toBe(9.50);
    expect(priceDetails.is_wholesale).toBe(true);
  });
});
```

### 2. Integration Tests
- Test DSA sale creation with wholesale pricing
- Test DSA stock assignment pricing
- Test DSA reconciliation calculations

### 3. Database Validation
- Verify no new DSA sales use retail pricing
- Monitor pricing consistency across all DSA operations
