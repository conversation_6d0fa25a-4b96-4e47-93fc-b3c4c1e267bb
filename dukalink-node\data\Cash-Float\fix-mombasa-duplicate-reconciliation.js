const path = require("path");
const {
  Branch,
  PosSession,
  PosSessionReconciliation,
} = require("../../src/models");
const sequelize = require("../../config/database");
const logger = require("../../src/utils/logger");

/**
 * FIX MOMBASA DUPLICATE RECONCILIATION ISSUE
 * 
 * Problem: Session 139 (Mombasa) has TWO reconciliations:
 * - Reconciliation 161: CORRECT values (34,821 cash, 96,381 float) - from our script
 * - Reconciliation 165: WRONG values (115,370 cash, -114,500 float) - from mobile app
 * 
 * Solution: Delete the incorrect reconciliation (165) and keep the correct one (161)
 */

async function fixMombasaDuplicateReconciliation() {
  const transaction = await sequelize.transaction();
  
  try {
    logger.info('🔧 FIXING MOMBASA DUPLICATE RECONCILIATION ISSUE');
    logger.info('==================================================');
    logger.info('📋 Problem: Session 139 has two reconciliations');
    logger.info('✅ Keeping: Reconciliation 161 (correct Excel values)');
    logger.info('❌ Removing: Reconciliation 165 (wrong mobile app values)');
    
    // Step 1: Verify the current state
    logger.info('\n📋 Step 1: Verifying current state...');
    
    const session139 = await PosSession.findByPk(139, {
      include: [
        {
          model: PosSessionReconciliation,
          as: 'reconciliation',
          required: false
        },
        {
          model: Branch,
          attributes: ['id', 'name']
        }
      ]
    });
    
    if (!session139) {
      throw new Error('Session 139 not found');
    }
    
    logger.info(`📍 Session 139 belongs to: ${session139.Branch.name}`);
    
    // Get all reconciliations for this session
    const allReconciliations = await PosSessionReconciliation.findAll({
      where: { pos_session_id: 139 },
      order: [['created_at', 'ASC']]
    });
    
    logger.info(`📊 Found ${allReconciliations.length} reconciliations for session 139:`);
    
    allReconciliations.forEach((rec, index) => {
      logger.info(`   ${index + 1}. ID: ${rec.id}, Cash: ${rec.closing_cash_balance}, Float: ${rec.closing_mpesa_float}`);
      logger.info(`      Notes: ${rec.notes}`);
      logger.info(`      Created: ${rec.created_at}`);
    });
    
    // Step 2: Identify correct and incorrect reconciliations
    logger.info('\n📋 Step 2: Identifying reconciliations...');
    
    const correctReconciliation = allReconciliations.find(rec => 
      parseFloat(rec.closing_cash_balance) === 34821 && 
      parseFloat(rec.closing_mpesa_float) === 96381
    );
    
    const incorrectReconciliations = allReconciliations.filter(rec => 
      !(parseFloat(rec.closing_cash_balance) === 34821 && 
        parseFloat(rec.closing_mpesa_float) === 96381)
    );
    
    if (!correctReconciliation) {
      throw new Error('Could not find the correct reconciliation with Excel values');
    }
    
    logger.info(`✅ Correct reconciliation: ID ${correctReconciliation.id}`);
    logger.info(`   Cash: ${correctReconciliation.closing_cash_balance}, Float: ${correctReconciliation.closing_mpesa_float}`);
    
    if (incorrectReconciliations.length === 0) {
      logger.info('✅ No incorrect reconciliations found - nothing to fix');
      await transaction.rollback();
      return { success: true, message: 'No duplicates found' };
    }
    
    logger.info(`❌ Found ${incorrectReconciliations.length} incorrect reconciliation(s):`);
    incorrectReconciliations.forEach(rec => {
      logger.info(`   ID: ${rec.id}, Cash: ${rec.closing_cash_balance}, Float: ${rec.closing_mpesa_float}`);
    });
    
    // Step 3: Delete incorrect reconciliations
    logger.info('\n📋 Step 3: Removing incorrect reconciliations...');
    
    for (const incorrectRec of incorrectReconciliations) {
      logger.info(`🗑️ Deleting reconciliation ${incorrectRec.id}...`);
      await incorrectRec.destroy({ transaction });
      logger.info(`✅ Successfully deleted reconciliation ${incorrectRec.id}`);
    }
    
    // Step 4: Verify the fix
    logger.info('\n📋 Step 4: Verifying the fix...');
    
    const remainingReconciliations = await PosSessionReconciliation.findAll({
      where: { pos_session_id: 139 },
      transaction
    });
    
    logger.info(`📊 Remaining reconciliations: ${remainingReconciliations.length}`);
    
    if (remainingReconciliations.length !== 1) {
      throw new Error(`Expected 1 reconciliation, found ${remainingReconciliations.length}`);
    }
    
    const finalRec = remainingReconciliations[0];
    logger.info(`✅ Final reconciliation: ID ${finalRec.id}`);
    logger.info(`   Cash: ${finalRec.closing_cash_balance}, Float: ${finalRec.closing_mpesa_float}`);
    
    // Verify it matches Excel values
    const cashMatches = Math.abs(parseFloat(finalRec.closing_cash_balance) - 34821) < 0.01;
    const floatMatches = Math.abs(parseFloat(finalRec.closing_mpesa_float) - 96381) < 0.01;
    
    if (!cashMatches || !floatMatches) {
      throw new Error('Final reconciliation does not match Excel values');
    }
    
    logger.info('✅ Final reconciliation matches Excel values perfectly');
    
    // Commit the transaction
    await transaction.commit();
    
    logger.info('\n🎉 MOMBASA DUPLICATE RECONCILIATION FIX COMPLETED');
    logger.info('================================================');
    logger.info('✅ Removed incorrect reconciliation(s)');
    logger.info('✅ Kept correct reconciliation with Excel values');
    logger.info('✅ Session 139 now has exactly one reconciliation');
    logger.info('✅ Values match Excel: Cash 34,821, Float 96,381');
    
    return {
      success: true,
      removedReconciliations: incorrectReconciliations.length,
      keptReconciliation: correctReconciliation.id,
      finalValues: {
        cash: finalRec.closing_cash_balance,
        float: finalRec.closing_mpesa_float
      }
    };
    
  } catch (error) {
    await transaction.rollback();
    logger.error('💥 Error fixing Mombasa duplicate reconciliation:', error);
    throw error;
  }
}

// Execute the fix
if (require.main === module) {
  fixMombasaDuplicateReconciliation()
    .then((result) => {
      logger.info('✅ Mombasa fix completed successfully');
      console.log('Result:', result);
      process.exit(0);
    })
    .catch((error) => {
      logger.error('💥 Mombasa fix failed:', error);
      process.exit(1);
    });
}

module.exports = { fixMombasaDuplicateReconciliation };
