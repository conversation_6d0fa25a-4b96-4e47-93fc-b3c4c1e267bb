# MPESA User Permissions Management Guide

## Current User Status

**Target User Found:**
- **Name**: Mpesa Department
- **ID**: 49
- **Email**: <EMAIL>
- **Current Role**: float_manager (ID: 7)
- **Branch**: HQ (ID: 1)
- **Status**: Active

## Current Permissions Analysis

The `float_manager` role currently has **limited permissions**:

### ✅ **Current Access (Read-Only Mostly)**
- Banking: read:any
- MPESA Float Balances: read:any
- MPESA Float Movements: read:any
- MPESA Float Reconciliations: read:any
- MPESA Float Top-ups: read:any + create:any
- MPESA Transactions: read:any
- MPESA Float Assignment: full CRUD
- Users: read:any

### ❌ **Missing Critical Permissions**
- Banking: create, update, delete
- MPESA Float Balances: create, update, delete
- MPESA Float Movements: create, update, delete
- MPESA Float Reconciliations: create, update, delete
- MPESA Float Top-ups: update, delete
- MPESA Transactions: create, update, delete
- POS Session Reconciliations: all actions
- Financial Reports: read access

## Recommended Solutions

### Option 1: Enhance Existing float_manager Role (RECOMMENDED)

**Pros:**
- ✅ Improves permissions for all float managers
- ✅ No user role changes needed
- ✅ Maintains existing role structure
- ✅ Immediate effect for "Mpesa Department" user

**Cons:**
- ⚠️ Affects all users with float_manager role

**Implementation:**
```sql
-- Execute the enhancement script
mysql -u username -p dukalink_api < scripts/grant-mpesa-permissions.sql
```

### Option 2: Create New mpesa_admin Role

**Pros:**
- ✅ Dedicated role for comprehensive MPESA management
- ✅ Doesn't affect other float_manager users
- ✅ More granular control
- ✅ Future-proof for specialized MPESA administrators

**Cons:**
- ⚠️ Requires user role change
- ⚠️ Creates new role to maintain

**Implementation:**
```sql
-- 1. Execute the script to create mpesa_admin role
mysql -u username -p dukalink_api < scripts/grant-mpesa-permissions.sql

-- 2. Update user to use new role
UPDATE users SET role_id = (SELECT id FROM roles WHERE name = 'mpesa_admin') WHERE id = 49;
```

## Complete Permissions Granted

### Banking Operations
- **Create**: Add new banking records
- **Read**: View all banking data
- **Update**: Modify banking records
- **Delete**: Remove banking records

### MPESA Float Management
- **Float Balances**: Full CRUD access to view and manage float balances
- **Float Movements**: Full CRUD access to create, track, and manage float transfers
- **Float Reconciliations**: Full CRUD access to reconcile float discrepancies
- **Float Top-ups**: Full CRUD access to manage float top-ups and funding
- **Float Assignments**: Full CRUD access to assign float to branches/users

### MPESA Transactions
- **Transaction Management**: Full CRUD access to MPESA transaction records
- **Transaction Monitoring**: View and manage all MPESA transactions
- **Transaction Reconciliation**: Reconcile MPESA transactions with float

### POS Session Reconciliations
- **Session Management**: Full CRUD access to POS session reconciliations
- **Float Reconciliation**: Reconcile POS session float with MPESA float
- **Discrepancy Management**: Identify and resolve float discrepancies

### Reporting and Analytics
- **Financial Reports**: Read access to all financial reports
- **Analytics**: Access to MPESA and float analytics
- **Reports**: General reporting access

### User Management
- **User Information**: Read access to user details for assignment purposes

## Implementation Steps

### Step 1: Choose Your Approach
```bash
# Option 1: Enhance existing float_manager role
# This affects all float managers but is simpler

# Option 2: Create new mpesa_admin role
# This is more targeted but requires role change
```

### Step 2: Execute the Permissions Script
```bash
# Navigate to your project directory
cd /path/to/dukalink-node

# Execute the SQL script
mysql -u your_username -p dukalink_api < scripts/grant-mpesa-permissions.sql
```

### Step 3: (Optional) Change User Role to mpesa_admin
```sql
-- Only if using Option 2
UPDATE users 
SET role_id = (SELECT id FROM roles WHERE name = 'mpesa_admin') 
WHERE id = 49;
```

### Step 4: Verify Permissions
```sql
-- Check user's current role
SELECT u.id, u.name, u.email, r.name as role_name 
FROM users u 
JOIN roles r ON u.role_id = r.id 
WHERE u.id = 49;

-- Check role permissions
SELECT role, resource, action 
FROM rbac_grants 
WHERE role = 'float_manager'  -- or 'mpesa_admin' if using Option 2
ORDER BY resource, action;
```

## Testing the Permissions

### 1. Login as Mpesa Department User
- Email: <EMAIL>
- Test access to MPESA float management features

### 2. Test Key Operations
- ✅ Create MPESA float top-up
- ✅ Create float movement between branches
- ✅ Create float reconciliation
- ✅ Update float balances
- ✅ View financial reports
- ✅ Manage POS session reconciliations

### 3. Verify API Access
Test these endpoints with the user's token:
- `POST /api/v1/mpesa-float-topups`
- `POST /api/v1/mpesa-float-movements`
- `POST /api/v1/mpesa-float-reconciliations`
- `PUT /api/v1/mpesa-float-balances/{id}`
- `GET /api/v1/financial-reports`

## Security Considerations

### ✅ **Safe Permissions**
- All permissions are scoped to banking and MPESA operations
- No access to sensitive areas like user management or system settings
- Read-only access to reports and analytics
- Proper RBAC implementation with resource-action granularity

### ⚠️ **Monitor Usage**
- Track permission usage through application logs
- Monitor for any unusual activity
- Regular permission audits
- Consider implementing approval workflows for critical operations

## Rollback Plan

If you need to revert the changes:

### Rollback Option 1 (Enhanced float_manager)
```sql
-- Remove the additional permissions granted to float_manager
DELETE FROM rbac_grants 
WHERE role = 'float_manager' 
AND action IN ('create:any', 'update:any', 'delete:any')
AND resource IN ('banking', 'mpesa-float-balances', 'mpesa-float-movements', 
                 'mpesa-float-reconciliations', 'mpesa-float-topups', 
                 'mpesa-transactions', 'pos-session-reconciliations');
```

### Rollback Option 2 (New mpesa_admin role)
```sql
-- Change user back to float_manager
UPDATE users SET role_id = 7 WHERE id = 49;

-- Remove mpesa_admin role and permissions
DELETE FROM rbac_grants WHERE role = 'mpesa_admin';
DELETE FROM roles WHERE name = 'mpesa_admin';
```

## Summary

**Recommended Action**: Use **Option 1** (Enhance float_manager role) because:
1. The "Mpesa Department" user already has the float_manager role
2. It's simpler to implement
3. It improves the role for all float managers
4. It maintains the existing role structure

**Expected Result**: The "Mpesa Department" user will have comprehensive access to:
- All banking operations
- Complete MPESA float management
- Float reconciliation capabilities
- Financial reporting
- POS session reconciliation for float management

This will enable them to perform all necessary banking and float management operations without restrictions.
