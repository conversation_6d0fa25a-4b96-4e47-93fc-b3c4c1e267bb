"use strict";

/**
 * <PERSON><PERSON><PERSON> to verify MPESA user permissions and test RBAC functionality
 * This script checks if the Mpesa Department user has the correct permissions
 */

require("dotenv").config();

const db = require("../src/models");
const sequelize = require("../config/database");
const logger = require("../src/utils/logger");
const rbacService = require("../src/services/rbac.service");

/**
 * Expected permissions for comprehensive MPESA and banking operations
 */
const EXPECTED_PERMISSIONS = {
  banking: ["create:any", "read:any", "update:any", "delete:any"],
  "mpesa-float-balances": [
    "create:any",
    "read:any",
    "update:any",
    "delete:any",
  ],
  "mpesa-float-movements": [
    "create:any",
    "read:any",
    "update:any",
    "delete:any",
  ],
  "mpesa-float-reconciliations": [
    "create:any",
    "read:any",
    "update:any",
    "delete:any",
  ],
  "mpesa-float-topups": ["create:any", "read:any", "update:any", "delete:any"],
  "mpesa-transactions": ["create:any", "read:any", "update:any", "delete:any"],
  mpesa_float: ["create:any", "read:any", "update:any", "delete:any"],
  mpesa_float_assignment: [
    "create:any",
    "read:any",
    "update:any",
    "delete:any",
  ],
  mpesa_float_movements: ["create:any", "read:any", "update:any", "delete:any"],
  mpesa_float_topups: ["create:any", "read:any", "update:any", "delete:any"],
  mpesa_transactions: ["create:any", "read:any", "update:any", "delete:any"],
  "pos-session-reconciliations": [
    "create:any",
    "read:any",
    "update:any",
    "delete:any",
  ],
  reports: ["read:any"],
  "financial-reports": ["read:any"],
  analytics: ["read:any"],
  "cash-float": ["create:any", "read:any", "update:any", "delete:any"],
  branches: ["read:any"],
  users: ["read:any"],
  profile: ["read:own"],
};

/**
 * Get user information and role
 */
async function getMpesaUserInfo() {
  try {
    const user = await db.User.findOne({
      where: {
        name: "Mpesa Department",
        email: "<EMAIL>",
      },
      include: [
        {
          model: db.Role,
          attributes: ["id", "name"],
        },
        {
          model: db.Branch,
          attributes: ["id", "name"],
        },
      ],
    });

    if (!user) {
      throw new Error("Mpesa Department user not found");
    }

    return {
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.Role?.name || "Unknown",
      roleId: user.role_id,
      branch: user.Branch?.name || "Unknown",
      branchId: user.branch_id,
      isActive: user.deleted_at === null,
    };
  } catch (error) {
    logger.error("Error getting Mpesa user info:", error);
    throw error;
  }
}

/**
 * Get all permissions for the float_manager role
 */
async function getFloatManagerPermissions() {
  try {
    const grants = await db.rbacGrants.findAll({
      where: { role: "float_manager" },
      order: [
        ["resource", "ASC"],
        ["action", "ASC"],
      ],
    });

    // Group permissions by resource
    const groupedPermissions = grants.reduce((acc, grant) => {
      if (!acc[grant.resource]) {
        acc[grant.resource] = [];
      }
      acc[grant.resource].push({
        action: grant.action,
        attributes: grant.attributes,
      });
      return acc;
    }, {});

    return {
      total: grants.length,
      raw: grants,
      grouped: groupedPermissions,
    };
  } catch (error) {
    logger.error("Error getting float_manager permissions:", error);
    throw error;
  }
}

/**
 * Check if all expected permissions are present
 */
function checkPermissionCompleteness(actualPermissions) {
  const results = {
    complete: [],
    incomplete: [],
    missing: [],
    extra: [],
  };

  // Check expected permissions
  Object.keys(EXPECTED_PERMISSIONS).forEach((resource) => {
    const expectedActions = EXPECTED_PERMISSIONS[resource];
    const actualActions = actualPermissions.grouped[resource]
      ? actualPermissions.grouped[resource].map((p) => p.action)
      : [];

    const missingActions = expectedActions.filter(
      (action) => !actualActions.includes(action)
    );

    if (missingActions.length === 0) {
      results.complete.push(resource);
    } else if (actualActions.length > 0) {
      results.incomplete.push({
        resource,
        missing: missingActions,
        present: actualActions,
      });
    } else {
      results.missing.push(resource);
    }
  });

  // Check for extra permissions
  Object.keys(actualPermissions.grouped).forEach((resource) => {
    if (!EXPECTED_PERMISSIONS[resource]) {
      results.extra.push({
        resource,
        actions: actualPermissions.grouped[resource].map((p) => p.action),
      });
    }
  });

  return results;
}

/**
 * Test RBAC functionality with the AccessControl library
 */
async function testRBACFunctionality() {
  try {
    logger.info("Testing RBAC functionality...");

    // Get the AccessControl instance
    const ac = await rbacService.getAccessControl();

    if (!ac) {
      throw new Error("AccessControl instance not available");
    }

    const testResults = {
      banking: {},
      mpesaFloat: {},
      reports: {},
    };

    // Test banking permissions
    testResults.banking.create = ac
      .can("float_manager")
      .createAny("banking").granted;
    testResults.banking.read = ac
      .can("float_manager")
      .readAny("banking").granted;
    testResults.banking.update = ac
      .can("float_manager")
      .updateAny("banking").granted;
    testResults.banking.delete = ac
      .can("float_manager")
      .deleteAny("banking").granted;

    // Test MPESA float permissions
    testResults.mpesaFloat.create = ac
      .can("float_manager")
      .createAny("mpesa-float-balances").granted;
    testResults.mpesaFloat.read = ac
      .can("float_manager")
      .readAny("mpesa-float-balances").granted;
    testResults.mpesaFloat.update = ac
      .can("float_manager")
      .updateAny("mpesa-float-balances").granted;
    testResults.mpesaFloat.delete = ac
      .can("float_manager")
      .deleteAny("mpesa-float-balances").granted;

    // Test reports permissions
    testResults.reports.read = ac
      .can("float_manager")
      .readAny("reports").granted;
    testResults.reports.financial = ac
      .can("float_manager")
      .readAny("financial-reports").granted;

    return testResults;
  } catch (error) {
    logger.error("Error testing RBAC functionality:", error);
    return null;
  }
}

/**
 * Generate a comprehensive permissions report
 */
function generatePermissionsReport(
  userInfo,
  permissions,
  completenessCheck,
  rbacTest
) {
  const report = {
    user: userInfo,
    permissions: {
      total: permissions.total,
      byResource: Object.keys(permissions.grouped).length,
    },
    completeness: {
      complete: completenessCheck.complete.length,
      incomplete: completenessCheck.incomplete.length,
      missing: completenessCheck.missing.length,
      extra: completenessCheck.extra.length,
    },
    rbacTest: rbacTest,
  };

  // Calculate completeness percentage
  const totalExpected = Object.keys(EXPECTED_PERMISSIONS).length;
  const completenessPercentage = Math.round(
    (completenessCheck.complete.length / totalExpected) * 100
  );
  report.completeness.percentage = completenessPercentage;

  return report;
}

/**
 * Display the verification results
 */
function displayResults(report, completenessCheck) {
  logger.info("📊 MPESA User Permissions Verification Report");
  logger.info("=".repeat(60));

  // User Information
  logger.info(`👤 User Information:`);
  logger.info(`   Name: ${report.user.name}`);
  logger.info(`   Email: ${report.user.email}`);
  logger.info(`   Role: ${report.user.role} (ID: ${report.user.roleId})`);
  logger.info(`   Branch: ${report.user.branch} (ID: ${report.user.branchId})`);
  logger.info(`   Status: ${report.user.isActive ? "Active" : "Inactive"}`);

  // Permissions Summary
  logger.info(`\n📋 Permissions Summary:`);
  logger.info(`   Total Permissions: ${report.permissions.total}`);
  logger.info(`   Resources Covered: ${report.permissions.byResource}`);
  logger.info(`   Completeness: ${report.completeness.percentage}%`);

  // Completeness Details
  logger.info(
    `\n✅ Complete Resources (${completenessCheck.complete.length}):`
  );
  completenessCheck.complete.forEach((resource) => {
    logger.info(`   ✓ ${resource}`);
  });

  if (completenessCheck.incomplete.length > 0) {
    logger.info(
      `\n⚠️  Incomplete Resources (${completenessCheck.incomplete.length}):`
    );
    completenessCheck.incomplete.forEach((item) => {
      logger.info(
        `   ⚠️  ${item.resource}: Missing ${item.missing.join(", ")}`
      );
    });
  }

  if (completenessCheck.missing.length > 0) {
    logger.info(
      `\n❌ Missing Resources (${completenessCheck.missing.length}):`
    );
    completenessCheck.missing.forEach((resource) => {
      logger.info(`   ❌ ${resource}`);
    });
  }

  if (completenessCheck.extra.length > 0) {
    logger.info(`\n➕ Extra Resources (${completenessCheck.extra.length}):`);
    completenessCheck.extra.forEach((item) => {
      logger.info(`   ➕ ${item.resource}: ${item.actions.join(", ")}`);
    });
  }

  // RBAC Test Results
  if (report.rbacTest) {
    logger.info(`\n🧪 RBAC Functionality Test:`);
    logger.info(`   Banking Operations:`);
    logger.info(`     Create: ${report.rbacTest.banking.create ? "✅" : "❌"}`);
    logger.info(`     Read: ${report.rbacTest.banking.read ? "✅" : "❌"}`);
    logger.info(`     Update: ${report.rbacTest.banking.update ? "✅" : "❌"}`);
    logger.info(`     Delete: ${report.rbacTest.banking.delete ? "✅" : "❌"}`);

    logger.info(`   MPESA Float Operations:`);
    logger.info(
      `     Create: ${report.rbacTest.mpesaFloat.create ? "✅" : "❌"}`
    );
    logger.info(`     Read: ${report.rbacTest.mpesaFloat.read ? "✅" : "❌"}`);
    logger.info(
      `     Update: ${report.rbacTest.mpesaFloat.update ? "✅" : "❌"}`
    );
    logger.info(
      `     Delete: ${report.rbacTest.mpesaFloat.delete ? "✅" : "❌"}`
    );

    logger.info(`   Reports:`);
    logger.info(
      `     General Reports: ${report.rbacTest.reports.read ? "✅" : "❌"}`
    );
    logger.info(
      `     Financial Reports: ${report.rbacTest.reports.financial ? "✅" : "❌"}`
    );
  }

  // Overall Status
  const isFullyConfigured =
    report.completeness.percentage >= 95 &&
    report.rbacTest &&
    report.rbacTest.banking.create &&
    report.rbacTest.mpesaFloat.create;

  logger.info(
    `\n🎯 Overall Status: ${isFullyConfigured ? "✅ FULLY CONFIGURED" : "⚠️  NEEDS ATTENTION"}`
  );

  if (!isFullyConfigured) {
    logger.info(`\n💡 Recommendations:`);
    if (report.completeness.percentage < 95) {
      logger.info(
        `   • Run the grant-mpesa-user-permissions.js script to add missing permissions`
      );
    }
    if (!report.rbacTest || !report.rbacTest.banking.create) {
      logger.info(`   • Check RBAC cache and refresh if necessary`);
    }
  }
}

/**
 * Main verification function
 */
async function verifyMpesaPermissions() {
  try {
    logger.info("🔍 Starting MPESA user permissions verification");

    // Connect to the database
    await sequelize.authenticate();
    logger.info("✅ Connected to database");

    // Step 1: Get user information
    const userInfo = await getMpesaUserInfo();
    logger.info(`Found user: ${userInfo.name} with role: ${userInfo.role}`);

    // Step 2: Get current permissions
    const permissions = await getFloatManagerPermissions();
    logger.info(
      `Retrieved ${permissions.total} permissions for float_manager role`
    );

    // Step 3: Check completeness
    const completenessCheck = checkPermissionCompleteness(permissions);

    // Step 4: Test RBAC functionality
    const rbacTest = await testRBACFunctionality();

    // Step 5: Generate report
    const report = generatePermissionsReport(
      userInfo,
      permissions,
      completenessCheck,
      rbacTest
    );

    // Step 6: Display results
    displayResults(report, completenessCheck);

    return { success: true, report, completenessCheck };
  } catch (error) {
    logger.error("❌ Error in verification script:", error);
    throw error;
  }
}

// Run the script if called directly
if (require.main === module) {
  verifyMpesaPermissions()
    .then((result) => {
      logger.info("✅ MPESA permissions verification completed!");
      process.exit(0);
    })
    .catch((error) => {
      logger.error("❌ Verification failed:", error.message);
      process.exit(1);
    });
}

module.exports = {
  verifyMpesaPermissions,
  getMpesaUserInfo,
  getFloatManagerPermissions,
  checkPermissionCompleteness,
  testRBACFunctionality,
};
