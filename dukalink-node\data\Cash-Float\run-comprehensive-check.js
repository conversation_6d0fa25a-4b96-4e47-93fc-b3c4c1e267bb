#!/usr/bin/env node

/**
 * COMPREHENSIVE DISCREPANCY CHECK RUNNER - READ-ONLY MODE
 * 
 * This script runs a comprehensive check for ALL branches to identify:
 * 1. Multiple reconciliations per session (like Mombasa had)
 * 2. Conflicting values between reconciliations
 * 3. Missing reconciliations
 * 4. Extreme or negative values
 * 5. Branches not in Excel
 * 6. Other data integrity issues
 * 
 * ⚠️ READ-ONLY MODE - NO DATABASE CHANGES WILL BE MADE
 * 
 * Run with: node data/cash-float/run-comprehensive-check.js
 */

const path = require('path');

// Set up environment
require('dotenv').config({ path: path.join(__dirname, '../../.env') });

// Import the comprehensive check function
const { comprehensiveDiscrepancyCheck } = require('./comprehensive-discrepancy-check');

console.log('🔍 COMPREHENSIVE DISCREPANCY CHECK - READ-ONLY MODE');
console.log('====================================================');
console.log('⚠️  NO DATABASE CHANGES WILL BE MADE');
console.log('🔧 CHECKING FOR:');
console.log('   • Multiple reconciliations per session');
console.log('   • Conflicting values between reconciliations');
console.log('   • Missing reconciliations');
console.log('   • Extreme values (> 1 million)');
console.log('   • Negative values');
console.log('   • Branches not in Excel');
console.log('   • Other data integrity issues');
console.log('====================================================\n');

comprehensiveDiscrepancyCheck()
  .then((issues) => {
    console.log('\n✅ Comprehensive discrepancy check completed!');
    
    const totalIssues = Object.values(issues).reduce((sum, arr) => sum + arr.length, 0);
    
    console.log('📊 SUMMARY:');
    console.log(`   • Multiple reconciliations: ${issues.multipleReconciliations.length}`);
    console.log(`   • Conflicting values: ${issues.conflictingValues.length}`);
    console.log(`   • Missing reconciliations: ${issues.missingReconciliations.length}`);
    console.log(`   • Unexpected branches: ${issues.unexpectedBranches.length}`);
    console.log(`   • Extreme values: ${issues.extremeValues.length}`);
    console.log(`   • Negative values: ${issues.negativeValues.length}`);
    console.log(`   • Total issues found: ${totalIssues}`);
    
    if (totalIssues > 0) {
      console.log('\n⚠️ ISSUES DETECTED:');
      console.log('📋 Review the detailed logs above for specific issues');
      console.log('🔧 Some issues may require cleanup before final update');
      
      if (issues.multipleReconciliations.length > 0) {
        console.log('\n🔄 MULTIPLE RECONCILIATIONS DETECTED:');
        console.log('   These branches have multiple reconciliations like Mombasa had:');
        issues.multipleReconciliations.forEach(issue => {
          console.log(`   • ${issue.branchName}: ${issue.reconciliationCount} reconciliations`);
        });
      }
      
      if (issues.conflictingValues.length > 0) {
        console.log('\n❌ CONFLICTING VALUES DETECTED:');
        console.log('   These sessions have reconciliations with different values:');
        issues.conflictingValues.forEach(issue => {
          console.log(`   • ${issue.branchName} (Session ${issue.sessionId})`);
        });
      }
      
      if (issues.extremeValues.length > 0) {
        console.log('\n⚠️ EXTREME VALUES DETECTED:');
        console.log('   These reconciliations have unusually high values:');
        issues.extremeValues.forEach(issue => {
          console.log(`   • ${issue.branchName}: Cash ${issue.cash.toLocaleString()}, Float ${issue.float.toLocaleString()}`);
        });
      }
      
    } else {
      console.log('\n🎉 NO ISSUES DETECTED!');
      console.log('✅ All data appears clean and consistent');
      console.log('🚀 Ready for final update execution');
    }
    
    console.log('\n💡 Next Steps:');
    if (totalIssues > 0) {
      console.log('   1. Review the issues identified above');
      console.log('   2. Create fix scripts for any critical issues');
      console.log('   3. Re-run this check after fixes');
      console.log('   4. Proceed with final update when clean');
    } else {
      console.log('   1. Data is clean - ready for final update');
      console.log('   2. Run the improved update script when ready');
    }
    
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 Comprehensive check failed:', error.message);
    console.error('📋 Check the error details above');
    
    if (error.stack) {
      console.error('\n📝 Error stack:');
      console.error(error.stack);
    }
    
    process.exit(1);
  });
