# Execute MPESA Permissions Scripts Guide

## Overview

This guide shows how to run the JavaScript scripts to grant comprehensive banking and MPESA float management permissions to the "Mpesa Department" user using the existing RBAC grants system.

## Scripts Created

### 1. `scripts/grant-mpesa-user-permissions.js`
- **Purpose**: Grants comprehensive MPESA and banking permissions to float_manager role
- **Target**: Enhances the existing float_manager role (used by "Mpesa Department" user)
- **Safety**: Uses transactions and proper error handling
- **Features**: 
  - Adds missing CRUD permissions for all banking and MPESA resources
  - Updates existing permissions if needed
  - Refreshes RBAC cache automatically
  - Provides detailed logging and progress tracking

### 2. `scripts/verify-mpesa-permissions.js`
- **Purpose**: Verifies that permissions were granted correctly
- **Features**:
  - Checks user information and role
  - Validates all expected permissions are present
  - Tests RBAC functionality with AccessControl library
  - Generates comprehensive report
  - Provides recommendations if issues found

## Execution Steps

### Step 1: Navigate to Project Directory
```bash
cd /path/to/dukalink-node
```

### Step 2: Run the Permission Grant Script
```bash
# Grant comprehensive MPESA and banking permissions
node scripts/grant-mpesa-user-permissions.js
```

**Expected Output:**
```
🚀 Starting MPESA user permissions grant script
✅ Connected to database
Found user: Mpesa Department (ID: 49) with role: float_manager
Found 18 existing permissions for float_manager role
Starting to add MPESA and banking permissions to float_manager role
Added: float_manager -> banking:create:any
Added: float_manager -> banking:update:any
Added: float_manager -> banking:delete:any
...
Permissions update completed:
  Added: 25 new permissions
  Updated: 3 existing permissions
  Skipped: 15 unchanged permissions
RBAC cache refreshed successfully
🎉 Successfully granted comprehensive MPESA and banking permissions!
```

### Step 3: Verify Permissions Were Applied
```bash
# Verify the permissions were granted correctly
node scripts/verify-mpesa-permissions.js
```

**Expected Output:**
```
🔍 Starting MPESA user permissions verification
✅ Connected to database
Found user: Mpesa Department with role: float_manager
Retrieved 43 permissions for float_manager role

📊 MPESA User Permissions Verification Report
============================================================
👤 User Information:
   Name: Mpesa Department
   Email: <EMAIL>
   Role: float_manager (ID: 7)
   Branch: HQ (ID: 1)
   Status: Active

📋 Permissions Summary:
   Total Permissions: 43
   Resources Covered: 18
   Completeness: 100%

✅ Complete Resources (18):
   ✓ banking
   ✓ mpesa-float-balances
   ✓ mpesa-float-movements
   ✓ mpesa-float-reconciliations
   ...

🧪 RBAC Functionality Test:
   Banking Operations:
     Create: ✅  Read: ✅  Update: ✅  Delete: ✅
   MPESA Float Operations:
     Create: ✅  Read: ✅  Update: ✅  Delete: ✅
   Reports:
     General Reports: ✅  Financial Reports: ✅

🎯 Overall Status: ✅ FULLY CONFIGURED
```

## What the Scripts Do

### Permissions Granted

The scripts grant the following comprehensive permissions to the `float_manager` role:

#### **Banking Operations**
- ✅ Create, Read, Update, Delete all banking records

#### **MPESA Float Management**
- ✅ **Float Balances**: Full CRUD access
- ✅ **Float Movements**: Full CRUD access  
- ✅ **Float Reconciliations**: Full CRUD access
- ✅ **Float Top-ups**: Full CRUD access
- ✅ **Float Assignments**: Full CRUD access (already existed)

#### **MPESA Transactions**
- ✅ **Transaction Management**: Full CRUD access
- ✅ **Transaction Monitoring**: Complete access

#### **POS Session Reconciliations**
- ✅ **Session Management**: Full CRUD access for float reconciliation

#### **Reports and Analytics**
- ✅ **Financial Reports**: Read access
- ✅ **Analytics**: Read access
- ✅ **General Reports**: Read access

#### **Supporting Resources**
- ✅ **Cash Float**: Full CRUD access
- ✅ **Branches**: Read access (for assignments)
- ✅ **Users**: Read access (existing)
- ✅ **Profile**: Read own profile (existing)

### Safety Features

#### **Transaction Safety**
- Uses database transactions for atomic operations
- Automatic rollback on errors
- No partial updates if something fails

#### **Existing Data Protection**
- Checks for existing permissions before adding
- Updates only if attributes differ
- Skips unchanged permissions
- No duplicate entries created

#### **RBAC Integration**
- Follows existing RBAC patterns in the codebase
- Uses proper Sequelize models
- Refreshes AccessControl cache automatically
- Maintains proper attributes format

#### **Comprehensive Logging**
- Detailed progress tracking
- Error reporting with context
- Success/failure counts
- Verification steps

## Troubleshooting

### If the Grant Script Fails

1. **Check Database Connection**
   ```bash
   # Verify .env file has correct database credentials
   cat .env | grep DB_
   ```

2. **Check User Exists**
   ```sql
   SELECT id, name, email, role_id FROM users WHERE name = 'Mpesa Department';
   ```

3. **Check RBAC Model**
   ```bash
   # Verify rbacGrants model is properly loaded
   node -e "const db = require('./src/models'); console.log(!!db.rbacGrants);"
   ```

### If Verification Shows Issues

1. **Incomplete Permissions**
   - Re-run the grant script
   - Check for database errors in logs

2. **RBAC Test Failures**
   - Restart the application to refresh cache
   - Check AccessControl configuration

3. **User Role Issues**
   - Verify user has float_manager role
   - Check role_id matches roles table

## Manual Verification

You can also manually verify the permissions in the database:

```sql
-- Check user information
SELECT u.id, u.name, u.email, r.name as role_name 
FROM users u 
JOIN roles r ON u.role_id = r.id 
WHERE u.name = 'Mpesa Department';

-- Check float_manager permissions
SELECT resource, action, attributes 
FROM rbac_grants 
WHERE role = 'float_manager' 
ORDER BY resource, action;

-- Count permissions by resource
SELECT resource, COUNT(*) as permission_count 
FROM rbac_grants 
WHERE role = 'float_manager' 
GROUP BY resource 
ORDER BY resource;
```

## Testing the Permissions

After running the scripts, test the permissions by:

1. **Login as Mpesa Department user**
   - Email: <EMAIL>
   - Use the existing password

2. **Test API Endpoints**
   ```bash
   # Test creating a float top-up
   curl -X POST http://localhost:3000/api/v1/mpesa-float-topups \
     -H "Authorization: Bearer <token>" \
     -H "Content-Type: application/json" \
     -d '{"tenant_id":1,"amount":50000,"payment_method":"bank_transfer"}'

   # Test creating a float movement
   curl -X POST http://localhost:3000/api/v1/mpesa-float-movements \
     -H "Authorization: Bearer <token>" \
     -H "Content-Type: application/json" \
     -d '{"tenant_id":1,"to_branch_id":3,"amount":10000}'
   ```

3. **Test Frontend Access**
   - Access MPESA float management pages
   - Try creating, updating, and deleting records
   - Verify all operations work without permission errors

## Rollback (If Needed)

If you need to revert the changes:

```sql
-- Remove the additional permissions (keep original read permissions)
DELETE FROM rbac_grants 
WHERE role = 'float_manager' 
AND action IN ('create:any', 'update:any', 'delete:any')
AND resource IN (
  'banking', 'mpesa-float-balances', 'mpesa-float-movements', 
  'mpesa-float-reconciliations', 'mpesa-float-topups', 
  'mpesa-transactions', 'pos-session-reconciliations',
  'cash-float'
);
```

## Summary

The JavaScript scripts provide a safe, comprehensive solution to grant the "Mpesa Department" user full access to all banking and MPESA float management operations while:

- ✅ Following existing RBAC patterns
- ✅ Using proper database transactions
- ✅ Maintaining data integrity
- ✅ Providing detailed verification
- ✅ Enabling easy rollback if needed

Run the scripts in order: **grant** → **verify** → **test** for best results.
